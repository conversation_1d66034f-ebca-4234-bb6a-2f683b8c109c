{% extends 'base.html' %}
{% load static %}

{% block title %}Tower Lights | Monitoring System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8">Tower Lights</h1>

    <!-- Alert container -->
    <div id="alert-container" class="mb-6 hidden">
        <div class="bg-yellow-900/70 backdrop-blur-sm border border-yellow-800 text-yellow-100 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Warning! </strong>
            <span id="alert-message" class="block sm:inline">Some towers are not reporting data.</span>
            <button id="close-alert" type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg class="fill-current h-6 w-6 text-yellow-100" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                    <title>Close</title>
                    <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                </svg>
            </button>
        </div>
    </div>

    <!-- Tower cards grid -->
    <div id="tower-cards-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <!-- Cards will be loaded here -->
    </div>

    <!-- Pagination -->
    <div class="flex justify-center items-center space-x-4 mt-8">
        <button id="prev-page" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                Previous
            </button>
        <div class="text-white">
                Page <span id="current-page">1</span> of <span id="total-pages">1</span>
        </div>
        <button id="next-page" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                Next
            </button>
    </div>
</div>

<!-- Tower Card Template -->
<template id="tower-card-template">
    <div class="tower-card rounded-xl overflow-hidden shadow-lg transition-all duration-500 transform hover:scale-105 bg-gray-800/90 backdrop-blur-md border border-gray-700/50">
        <div class="card-header p-4 text-white flex flex-col md:flex-row md:justify-between md:items-center bg-gradient-to-r from-gray-800/90 to-gray-700/90 border-b border-gray-700/50">
            <div class="flex items-center space-x-2">
                <h2 class="text-xl font-bold tower-name"></h2>
                <span class="status-badge px-3 py-1 rounded-full text-xs font-semibold"></span>
            </div>
            <div class="flex space-x-2 mt-2 md:mt-0">
                <span class="network-badge px-2 py-1 rounded-full text-xs font-semibold"></span>
                <span class="onoff-badge px-2 py-1 rounded-full text-xs font-semibold"></span>
            </div>
        </div>

        <div class="card-body p-6 space-y-6">
            <!-- Status indicator -->
            <div class="flex justify-center items-center">
                <div class="status-indicator w-3 h-3 rounded-full"></div>
                <span class="ml-2 text-sm font-medium text-gray-300 status-text"></span>
            </div>

            <!-- Metrics Grid -->
            <div class="grid grid-cols-2 gap-6">
                <!-- Voltage -->
                <div class="metric-container">
                    <div class="metric-ring">
                        <svg viewBox="0 0 120 120">
                            <circle class="progress-bg" cx="60" cy="60" r="54" />
                            <circle class="progress-ring voltage-ring" cx="60" cy="60" r="54" />
                        </svg>
                        <div class="metric-value">
                            <span class="value voltage-value">0</span>
                            <span class="unit">V</span>
                        </div>
                    </div>
                    <div class="text-center mt-2 text-sm text-gray-400">Voltage</div>
                </div>

                <!-- Current -->
                <div class="metric-container">
                    <div class="metric-ring">
                        <svg viewBox="0 0 120 120">
                            <circle class="progress-bg" cx="60" cy="60" r="54" />
                            <circle class="progress-ring current-ring" cx="60" cy="60" r="54" />
                        </svg>
                        <div class="metric-value">
                            <span class="value current-value">0</span>
                            <span class="unit">A</span>
                        </div>
                    </div>
                    <div class="text-center mt-2 text-sm text-gray-400">Current</div>
                </div>

                <!-- Power -->
                <div class="metric-container">
                    <div class="metric-ring">
                        <svg viewBox="0 0 120 120">
                            <circle class="progress-bg" cx="60" cy="60" r="54" />
                            <circle class="progress-ring power-ring" cx="60" cy="60" r="54" />
                        </svg>
                        <div class="metric-value">
                            <span class="value power-value">0</span>
                            <span class="unit">W</span>
                </div>
            </div>
                    <div class="text-center mt-2 text-sm text-gray-400">Power</div>
                </div>

                <!-- Light Intensity -->
                <div class="metric-container">
                    <div class="metric-ring">
                        <svg viewBox="0 0 120 120">
                            <circle class="progress-bg" cx="60" cy="60" r="54" />
                            <circle class="progress-ring light-ring" cx="60" cy="60" r="54" />
                        </svg>
                        <div class="metric-value">
                            <span class="value light-value">0</span>
                            <span class="unit">%</span>
                        </div>
                    </div>
                    <div class="text-center mt-2 text-sm text-gray-400">Light Intensity</div>
                </div>
            </div>

            <!-- Location Info -->
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-700/30 rounded-lg p-3">
                    <div class="text-xs text-gray-400">Latitude</div>
                    <div class="latitude text-sm font-mono text-gray-200"></div>
                </div>
                <div class="bg-gray-700/30 rounded-lg p-3">
                    <div class="text-xs text-gray-400">Longitude</div>
                    <div class="longitude text-sm font-mono text-gray-200"></div>
                </div>
            </div>

            <!-- Last Updated -->
            <div class="text-xs text-gray-500 last-updated mt-4"></div>
        </div>
    </div>
</template>

{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM Content Loaded');

    // Constants
    const ACTIVE_COLOR = '#3B82F6';  // Blue
    const FAULTY_COLOR = '#EF4444';  // Red
    const MAINTENANCE_COLOR = '#F59E0B';  // Yellow
    const INACTIVE_COLOR = '#6B7280';  // Gray
    const DATA_REFRESH_INTERVAL = 1000;  // 1 second
    const DAY_START_HOUR = 6;  // 6 AM
    const DAY_END_HOUR = 18;   // 6 PM

    // Base elements
    const alertContainer = document.getElementById('alert-container');
    const alertMessage = document.getElementById('alert-message');
    const closeAlertBtn = document.getElementById('close-alert');
    const prevPageBtn = document.getElementById('prev-page');
    const nextPageBtn = document.getElementById('next-page');
    const currentPageSpan = document.getElementById('current-page');
    const totalPagesSpan = document.getElementById('total-pages');
    const towerCardsContainer = document.getElementById('tower-cards-container');

    console.log('Elements found:', {
        alertContainer,
        alertMessage,
        closeAlertBtn,
        prevPageBtn,
        nextPageBtn,
        currentPageSpan,
        totalPagesSpan,
        towerCardsContainer
    });

    let currentPage = 1;
    let totalPages = 1;

    // Show initial alert
    alertMessage.textContent = 'Initializing real-time data...';
    alertContainer.classList.remove('hidden');

    // Close alert when clicking the close button
    closeAlertBtn.addEventListener('click', function() {
        alertContainer.classList.add('hidden');
    });

    // Set up pagination event listeners
    prevPageBtn.addEventListener('click', function() {
        console.log('Previous page clicked');
        if (currentPage > 1) {
            currentPage--;
            loadTowerData(currentPage);
        }
    });

    nextPageBtn.addEventListener('click', function() {
        console.log('Next page clicked');
        if (currentPage < totalPages) {
            currentPage++;
            loadTowerData(currentPage);
        }
    });

    function loadTowerData(page, isUpdate = false) {
        console.log(`Loading tower data for page ${page}...`);

        fetch(`/api/tower-data/?page=${page}`)
            .then(response => {
                console.log('API Response status:', response.status);
                console.log('API Response headers:', Object.fromEntries(response.headers.entries()));
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.text().then(text => {
                    console.log('Raw API response:', text);
                    try {
                        return JSON.parse(text);
                    } catch (e) {
                        console.error('JSON parse error:', e);
                        throw new Error('Invalid JSON response');
                    }
                });
            })
            .then(data => {
                console.log('Parsed tower data:', data);

                // Update pagination info
                currentPage = data.pagination.current_page;
                totalPages = data.pagination.total_pages;
                currentPageSpan.textContent = currentPage;
                totalPagesSpan.textContent = totalPages;
                prevPageBtn.disabled = currentPage <= 1;
                nextPageBtn.disabled = currentPage >= totalPages;

                // Clear container if not an update
                if (!isUpdate) {
                    console.log('Clearing container');
                    towerCardsContainer.innerHTML = '';
                }

                // Process tower data
                console.log(`Processing ${data.towers.length} towers`);
                let missingDataCount = 0;
                data.towers.forEach(tower => {
                    console.log('Processing tower:', tower);
                    // Get or create card
                    let card = document.getElementById(`tower-card-${tower.id}`);
                    if (!card) {
                        console.log('Creating new card for tower:', tower.id);
                        card = createTowerCard(tower);
                        towerCardsContainer.appendChild(card);
                    }

                    // Update the card with tower data
                    console.log('Updating card with data:', tower.sensor_data);
                    updateTowerCard(card, tower, tower.sensor_data);

                    // Check if this is a sensor tower with no real data
                    // Only towers 1 and 2 are connected to microcontrollers
                    if (tower.id <= 2 && !tower.sensor_data.sensor_value) {
                        missingDataCount++;
                    }
                });

                // Update alert message only for sensor towers
                if (missingDataCount > 0) {
                    alertMessage.textContent = `${missingDataCount} sensor tower(s) not reporting real data.`;
                    alertContainer.classList.remove('hidden');
                        } else {
                    alertContainer.classList.add('hidden');
                }
            })
            .catch(error => {
                console.error('Error loading tower data:', error);
                alertMessage.textContent = 'Error connecting to server. Please try again later.';
                alertContainer.classList.remove('hidden');
            });
    }

    function createTowerCard(tower) {
        console.log('Creating tower card for:', tower);
        const template = document.getElementById('tower-card-template');
        if (!template) {
            console.error('Tower card template not found!');
            return null;
        }

        const card = template.content.cloneNode(true).firstElementChild;
        card.id = `tower-card-${tower.id}`;

        // Set tower name
        const nameElement = card.querySelector('.tower-name');
        if (nameElement) {
            nameElement.textContent = tower.name;
        }

        // Set coordinates
        const latElement = card.querySelector('.latitude');
        const lngElement = card.querySelector('.longitude');
        if (latElement) latElement.textContent = tower.latitude.toFixed(4);
        if (lngElement) lngElement.textContent = tower.longitude.toFixed(4);

        console.log('Created card:', card);
        return card;
    }

    function updateTowerCard(card, tower, data) {
        console.log('Updating tower card:', { tower, data });
        if (!card) {
            console.error('Card element not found!');
            return;
        }

        // Check if it's night time (between 6 PM and 6 AM)
        const now = new Date();
        const currentHour = now.getHours();
        const isNightTime = currentHour >= DAY_END_HOUR || currentHour < DAY_START_HOUR;

        // Keep the tower's actual status (active, inactive, faulty, maintenance)
        // 'active' status means the tower is fully functional (not faulty)
        // The light can be either ON or OFF based on time of day
        let newStatus = tower.status;

        // We don't change the status based on time of day anymore
        // The status reflects the tower's functionality, not whether the light is ON/OFF

        // Update status
        updateCardStatus(card, newStatus);

        // Update metrics
        if (data) {
            console.log('Updating metrics with data:', data);
            // Update ring gauges
            updateMetricRing(card.querySelector('.voltage-ring'), data.voltage, 240);
            updateMetricRing(card.querySelector('.current-ring'), data.current, 2);
            updateMetricRing(card.querySelector('.power-ring'), data.power, 480);
            updateMetricRing(card.querySelector('.light-ring'), data.light_level, 100);

            // Update values
            const voltageValue = card.querySelector('.voltage-value');
            const currentValue = card.querySelector('.current-value');
            const powerValue = card.querySelector('.power-value');
            const lightValue = card.querySelector('.light-value');

            if (voltageValue) voltageValue.textContent = parseFloat(data.voltage).toFixed(1);
            if (currentValue) currentValue.textContent = parseFloat(data.current).toFixed(2);
            if (powerValue) powerValue.textContent = parseFloat(data.power).toFixed(1);
            if (lightValue) lightValue.textContent = parseInt(data.light_level);

            // Update timestamp
            const lastUpdated = card.querySelector('.last-updated');
            if (lastUpdated) {
                const timestamp = new Date(data.timestamp);
                lastUpdated.textContent = `Last updated: ${timestamp.toLocaleTimeString()}`;
            }

            card.classList.remove('no-data');
        } else {
            console.log('No data available for tower');
            card.classList.add('no-data');
            const lastUpdated = card.querySelector('.last-updated');
            if (lastUpdated) {
                lastUpdated.textContent = 'No data available';
            }
        }

        // Add update animation
        {#// card.classList.add('animate-update');#}
        setTimeout(() => card.classList.remove('animate-update'), 1000);

        // Network connectivity badge
        const networkBadge = card.querySelector('.network-badge');
        const onoffBadge = card.querySelector('.onoff-badge');
        let isOnline = false;
        // Determine if the light is ON based on time of day and tower status
        // Lights are ON at night (6pm-6am) if the tower is not faulty/maintenance
        let isOn = isNightTime && newStatus !== 'faulty' && newStatus !== 'maintenance';
        let lastTimestamp = data && data.timestamp ? new Date(data.timestamp) : null;

        // For towers 3-20, always consider them online
        // Only towers 1-2 should be checked for real connectivity
        if (tower.id >= 3) {
            isOnline = true;
        } else if (lastTimestamp && (now - lastTimestamp) < 2 * 60 * 1000) {
            // For towers 1-2, consider offline if no data or last update > 2 minutes ago
            isOnline = true;
        }
        if (networkBadge) {
            if (isOnline) {
                networkBadge.textContent = 'Online';
                networkBadge.className = 'network-badge bg-green-900 text-green-200';
            } else {
                networkBadge.textContent = 'Offline';
                networkBadge.className = 'network-badge bg-red-900 text-red-200';
            }
        }
        if (onoffBadge) {
            if (isOn) {
                onoffBadge.textContent = 'On';
                onoffBadge.className = 'onoff-badge bg-blue-900 text-blue-200';
            } else {
                onoffBadge.textContent = 'Off';
                onoffBadge.className = 'onoff-badge bg-gray-700 text-gray-300';
            }
        }
        // If offline, reset metrics and mark as no-data
        // This should only apply to towers 1-2 which are connected to microcontrollers
        if (!isOnline && tower.id <= 2) {
            // Reset metrics
            ['voltage-value','current-value','power-value','light-value'].forEach(cls => {
                let el = card.querySelector('.' + cls);
                if (el) el.textContent = '--';
            });
            // Reset rings
            ['voltage-ring','current-ring','power-ring','light-ring'].forEach(cls => {
                let ring = card.querySelector('.' + cls);
                if (ring) ring.style.strokeDashoffset = 339.292;
            });
            // Last updated
            const lastUpdated = card.querySelector('.last-updated');
            if (lastUpdated) lastUpdated.textContent = 'No communication';
            card.classList.add('no-data');
            return;
        }

        // For towers 3-20, ensure they always have 'active' status
        if (tower.id >= 3 && tower.id <= 20 && newStatus !== 'active') {
            console.log(`Forcing tower ${tower.id} to 'active' status (was '${newStatus}')`);
            newStatus = 'active';
            updateCardStatus(card, newStatus);
        }

        // Send status update to server if status changed
        if (newStatus !== tower.status) {
            fetch(`/api/tower/${tower.id}/status/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    status: newStatus
                })
            }).catch(error => console.error('Error updating tower status:', error));
        }
    }

    // Helper function to get CSRF token
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function getStatusBadgeClass(status) {
        switch(status.toLowerCase()) {
            case 'active':
                return 'status-green';
            case 'faulty':
                return 'status-red';
            case 'maintenance':
                return 'status-orange';
            case 'inactive':
            default:
                return 'status-gray';
        }
    }

    function updateCardStatus(card, status) {
        const badge = card.querySelector('.status-badge');
        if (badge) {
            badge.className = 'status-badge ' + getStatusBadgeClass(status);
            badge.textContent = capitalizeFirstLetter(status);
        }
        const statusIndicator = card.querySelector('.status-indicator');
        if (statusIndicator) {
            statusIndicator.className = 'status-indicator w-3 h-3 rounded-full ' + getStatusBadgeClass(status);
        }
        const statusText = card.querySelector('.status-text');
        if (statusText) {
            statusText.textContent = capitalizeFirstLetter(status);
            statusText.className = 'ml-2 text-sm font-medium status-badge ' + getStatusBadgeClass(status);
        }
    }

    function updateMetricRing(ring, value, maxValue) {
        if (!ring) return;

        const percentage = (value / maxValue) * 100;
        const circumference = 2 * Math.PI * 54;  // r=54 from the SVG
        const offset = circumference - (percentage / 100) * circumference;

        ring.style.strokeDasharray = `${circumference} ${circumference}`;
        ring.style.strokeDashoffset = offset;

        // Update color based on percentage
        let color;
        if (percentage < 30) {
            color = FAULTY_COLOR;
        } else if (percentage < 70) {
            color = MAINTENANCE_COLOR;
        } else {
            color = ACTIVE_COLOR;
        }
        ring.style.stroke = color;
    }

    function capitalizeFirstLetter(string) {
        return string.charAt(0).toUpperCase() + string.slice(1);
    }

    // Load initial data
    console.log('Loading initial data...');
    loadTowerData(currentPage);

    // Set up real-time data updates
    console.log('Setting up real-time updates...');
    setInterval(function() {
        loadTowerData(currentPage, true);
    }, DATA_REFRESH_INTERVAL);
});

function updateCard(card, data) {
    // Get all the elements that need updating
    const statusIndicator = card.querySelector('.status-indicator');
    const lastUpdate = card.querySelector('.last-update');
    const voltageRing = card.querySelector('.voltage-ring');
    const currentRing = card.querySelector('.current-ring');
    const powerRing = card.querySelector('.power-ring');
    const lightRing = card.querySelector('.light-ring');

    // Update status indicator
    const status = data.status.toLowerCase();
    statusIndicator.className = `status-indicator w-3 h-3 rounded-full ${getStatusColor(status)}`;

    // Update timestamp
    lastUpdate.textContent = formatTimestamp(data.last_update);

    // Update metric rings with debounced animation
    updateMetricRing(voltageRing, data.voltage, 240);
    updateMetricRing(currentRing, data.current, 1);
    updateMetricRing(powerRing, data.power, 240);
    updateMetricRing(lightRing, data.light_intensity, 100);

    // Add a subtle update animation
    card.classList.remove('animate-update');
    // Use requestAnimationFrame for smoother animation
    requestAnimationFrame(() => {
        card.classList.add('animate-update');
    });
}

// Debounce function to prevent too frequent updates
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Optimize metric ring updates
function updateMetricRing(ring, value, maxValue) {
    if (!ring) return;

    const progressRing = ring.querySelector('.progress-ring');
    const valueDisplay = ring.querySelector('.value');
    const circumference = 2 * Math.PI * 40; // Assuming radius of 40

    // Calculate the progress
    const progress = Math.min(Math.max(value / maxValue, 0), 1);
    const offset = circumference - (progress * circumference);

    // Update the ring
    progressRing.style.strokeDashoffset = offset;

    // Update the value display
    if (valueDisplay) {
        valueDisplay.textContent = value.toFixed(1);
    }
}

// Throttle the fetchData function to prevent too frequent updates
const throttledFetchData = throttle(fetchData, 1000);

function throttle(func, limit) {
    let inThrottle;
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    }
}
</script>

<style>
    :root {
    --ring-size: 339.292; /* 2 * PI * 54 */
    --ring-width: 8;
    --animation-timing: cubic-bezier(0.4, 0, 0.2, 1);
    --active-color: #3B82F6;
    --faulty-color: #EF4444;
    --maintenance-color: #F59E0B;
    --inactive-color: #6B7280;
}

/* Card Styles */
.tower-card {
    transition: all 0.3s var(--animation-timing);
    transform: translateZ(0); /* Enable hardware acceleration */
}

.tower-card:hover {
    transform: translateY(-5px) translateZ(0);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
}

/* Metric Ring Styles */
.metric-container {
    position: relative;
    width: 100px;
    height: 100px;
    margin: 0 auto;
}

.metric-ring {
    position: relative;
    width: 100%;
    height: 100%;
}

.metric-ring svg {
    transform: rotate(-90deg);
}

.progress-bg {
    fill: none;
    stroke: rgba(255, 255, 255, 0.1);
    stroke-width: var(--ring-width);
}

.progress-ring {
    fill: none;
    stroke-width: var(--ring-width);
    stroke-linecap: round;
    stroke-dasharray: var(--ring-size);
    stroke-dashoffset: var(--ring-size);
    transition: stroke-dashoffset 0.5s var(--animation-timing),
                stroke 0.3s var(--animation-timing);
}

.metric-value {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: white;
}

.value {
    display: block;
    font-size: 1.25rem;
    font-weight: bold;
    line-height: 1;
}

.unit {
    font-size: 0.75rem;
    opacity: 0.7;
}

/* Status Indicator */
.status-indicator {
    transition: box-shadow 0.3s ease;
}

/* Card Update Animation - Only on data change */
.animate-update {
    animation: subtle-update 0.5s var(--animation-timing);
}

@keyframes subtle-update {
    0% { opacity: 0.95; }
    50% { opacity: 1; }
    100% { opacity: 0.95; }
}

/* No Data State */
.no-data .progress-ring {
    stroke: rgba(255, 255, 255, 0.1);
}

.no-data .value,
.no-data .unit {
    opacity: 0.5;
}

/* Dark Mode Optimization */
@media (prefers-color-scheme: dark) {
    .tower-card {
        background: rgba(17, 24, 39, 0.9);
    }
}

/* Glass Morphism Effects */
.card-header {
    backdrop-filter: blur(8px);
}

.metric-container {
    backdrop-filter: blur(4px);
}

.status-badge {
    display: inline-block;
    padding: 2px 10px;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 4px;
}
.status-green { background: rgba(34,197,94,0.15); color: #22c55e; }
.status-red { background: rgba(239,68,68,0.15); color: #ef4444; }
.status-orange { background: rgba(251,191,36,0.15); color: #fbbf24; }
.status-gray { background: rgba(156,163,175,0.15); color: #9ca3af; }
</style>
{% endblock %}

{% block extra_css %}
<style>
    /* Card styles */
    .tower-card {
        background: rgba(31, 41, 55, 0.95);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(75, 85, 99, 0.2);
        transition: all 0.3s ease;
    }

    .tower-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
    }

    /* Status colors */
    .status-active {
        background-color: rgba(59, 130, 246, 0.2);
        color: rgb(147, 197, 253);
    }

    .status-faulty {
        background-color: rgba(239, 68, 68, 0.2);
        color: rgb(252, 165, 165);
    }

    .status-maintenance {
        background-color: rgba(245, 158, 11, 0.2);
        color: rgb(253, 230, 138);
    }

    .status-inactive {
        background-color: rgba(107, 114, 128, 0.2);
        color: rgb(209, 213, 219);
    }

    /* Progress rings */
    .progress-bg {
        fill: none;
        stroke: rgba(255, 255, 255, 0.1);
        stroke-width: 8;
    }

    .progress-ring {
        fill: none;
        stroke-width: 8;
        stroke-linecap: round;
        transform: rotate(-90deg);
        transform-origin: 50% 50%;
        transition: all 0.3s ease;
    }

    /* Animation */
    .animate-update {
        animation: pulse 0s cubic-bezier(0, 0, 0.2, 1);
    }

    @keyframes pulse {
        0%, 100% {
            opacity: 1;
        }
        50% {
            opacity: 0.7;
        }
    }

    /* Loading state */
    .loading {
        position: relative;
    }

    .loading::after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        backdrop-filter: blur(4px);
        display: flex;
        justify-content: center;
        align-items: center;
        font-size: 1.2rem;
        color: white;
    }
</style>
{% endblock %}




