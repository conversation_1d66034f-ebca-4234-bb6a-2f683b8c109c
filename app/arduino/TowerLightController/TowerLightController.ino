#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <time.h>
#include "config.h" // Include the configuration file

// Timing Configuration for Serial display
unsigned long lastDisplayUpdate = 0;

// Sensor Variables
float voltage = 0.0;
float current = 0.0;
float power = 0.0;
int lightRaw = 0;
float lightLux = 0.0;
int lightPercent = 0;

// Status Variables
String currentStatus = "inactive";
String faultType = "";
bool relayState = false;
bool manualOverride = false;
unsigned long manualOverrideStartTime = 0;

// Fault Flags
bool faultUnderVoltage = false;
bool faultOverVoltage = false;
bool faultLowCurrent = false;
bool faultHighCurrent = false;
bool faultLowPower = false;

// Timing variables for data sending
unsigned long lastSensorUpdate = 0;
unsigned long lastStatusUpdate = 0;

// Function Prototypes
void connectToWiFi();
void updateSensors();
void sendSensorData();
void updateStatus();
void sendStatusUpdate();
void controlRelay(bool state);
void checkFaults();
void displayStatus();
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
void blinkLED(int times, int delayMs);
void setManualOverride(bool manualState, bool newRelayState);

void setup() {
  Serial.begin(115200);
  delay(1000); 
  
  Serial.println("\n\n=== Tower Light Controller Starting ===");
  Serial.printf("Tower ID: %d, Firmware: %s\n", TOWER_ID, FIRMWARE_VERSION);
  Serial.flush();

  pinMode(RELAY_PIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT); 

  digitalWrite(RELAY_PIN, LOW); 
  relayState = false;
  digitalWrite(STATUS_LED_PIN, LOW); 

  analogSetPinAttenuation(VOLTAGE_PIN, ADC_11db); 
  analogSetPinAttenuation(CURRENT_PIN, ADC_11db); 
  analogSetPinAttenuation(LDR_PIN, ADC_11db);
  Serial.println("ADC attenuation set.");
  Serial.flush();

  Serial.println("Using fixed sensor parameters from config.h.");
  Serial.flush();

  connectToWiFi();
  
  if (WiFi.status() == WL_CONNECTED) {
    updateSensors(); 
    displayStatus();
    currentStatus = "inactive";
    configTime(TIMEZONE_OFFSET_GMT_HOURS * 3600, TIMEZONE_DAYLIGHT_OFFSET_SEC, NTP_SERVER_1, NTP_SERVER_2); 
    Serial.println("Initial setup complete.");
  } else {
    Serial.println("WiFi not connected. Some functionalities might be limited.");
  }
  Serial.flush();
  
  Serial.println("Setup finished.");
  Serial.flush();
}

void loop() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi connection lost. Reconnecting...");
    Serial.flush();
    connectToWiFi();
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("Reconnect failed. Delaying...");
        Serial.flush();
        delay(WIFI_RETRY_DELAY); 
        return; 
    }
    Serial.println("Reconnected to WiFi.");
    Serial.flush();
  }
  
  updateSensors();
  checkFaults(); 
  updateStatus(); 

  unsigned long currentTime = millis();

  if (currentTime - lastSensorUpdate >= DATA_SEND_INTERVAL_MS) {
    sendSensorData();
    lastSensorUpdate = currentTime;
  }
  
  if (currentTime - lastStatusUpdate >= STATUS_UPDATE_INTERVAL_MS) {
    sendStatusUpdate(); 
    lastStatusUpdate = currentTime;
  }
  
  if (currentTime - lastDisplayUpdate >= DISPLAY_UPDATE_INTERVAL_MS) {
    displayStatus();
    lastDisplayUpdate = currentTime;
  }
  
  delay(READ_INTERVAL_MS); 
}

void connectToWiFi() {
  Serial.print("Connecting to WiFi: '"); 
  Serial.print(WIFI_SSID);                             
  Serial.println("'");                                  
  Serial.flush();
  
  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);
  
  Serial.println("WiFi.begin() called."); 
  Serial.flush();

  unsigned long startTime = millis();
  int retries = 0;

  Serial.print("Connecting...");
  Serial.flush();
  while (WiFi.status() != WL_CONNECTED && retries < WIFI_MAX_RETRIES) {
    delay(WIFI_RETRY_DELAY);
    Serial.print(".");
    Serial.flush();
    retries++;
    if (WIFI_CONNECT_TIMEOUT_MS > 0 && (millis() - startTime > WIFI_CONNECT_TIMEOUT_MS) ) {
        Serial.println("\nConnection attempt timed out.");
        Serial.flush();
        return; 
    }
  }
  Serial.println(); 
  
  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi Connected!");
    Serial.print("IP Address: ");
    Serial.println(WiFi.localIP());
    blinkLED(3, 200); 
  } else {
    Serial.println("Failed to connect to WiFi.");
    Serial.printf("Last WiFi status: %d\n", WiFi.status());
  }
  Serial.flush();
}

void updateSensors() {
  // --- Voltage Sensor Reading ---
  int adc_value_v = analogRead(VOLTAGE_PIN);
  float adc_voltage_v = (adc_value_v * ADC_VREF) / ADC_MAX; 
  // voltage = adc_voltage_v * VOLTAGE_DIVIDER_RATIO; // Uses pre-calculated ratio
  // Or, calculate directly from R1 and R2 for clarity if preferred:
  voltage = adc_voltage_v * ( (VOLTAGE_R1 + VOLTAGE_R2) / VOLTAGE_R2 );


  if (voltage < VOLTAGE_SENSE_NOISE_THRESHOLD || voltage > VOLTAGE_SENSE_MAX_EXPECTED) {
    voltage = 0.0f;
  }

  // --- Current Sensor Reading ---
  unsigned long totalADC_current = 0;
  for (int i = 0; i < CURRENT_NUM_SAMPLES; i++) { 
    totalADC_current += analogRead(CURRENT_PIN); 
    delayMicroseconds(100); 
  }
  double avgADC_current = totalADC_current / (double)CURRENT_NUM_SAMPLES;
  double vout_current = (avgADC_current / ADC_MAX) * ADC_VREF; 
  current = (vout_current - CURRENT_ZERO_POINT) / CURRENT_SCALE_FACTOR; 
  
  if (abs(current) < CURRENT_NOISE_THRESHOLD || current < 0 || current > CURRENT_MAX_EXPECTED) {
    current = 0.0f;
  }

  // Calculate power
  power = voltage * current;
  if (power < 0) power = 0.0f;
  
  // --- LDR Sensor Reading ---
  lightRaw = analogRead(LDR_PIN);
  float ldrVoltage = (lightRaw / ADC_MAX) * ADC_VREF;
  
  float ldrResistance = 0;
  if (ldrVoltage > 0.001 && ldrVoltage < (ADC_VREF - 0.001) ) {
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - ldrVoltage) / ldrVoltage;
  } else if (ldrVoltage <= 0.001) { 
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - 0.001f) / 0.001f; 
      if (ldrResistance <= 0) ldrResistance = 1e7f; 
  } else { 
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - (ADC_VREF - 0.001f)) / (ADC_VREF - 0.001f);
      if (ldrResistance <= 0) ldrResistance = 10.0f; 
  }

  if (ldrResistance > 0) {
      lightLux = (LUX_EST_M / ldrResistance) + LUX_EST_C;
  } else { 
      lightLux = MAX_EXPECTED_LUX; 
  }

  if (lightLux < 0) lightLux = 0;
  if (lightLux > MAX_EXPECTED_LUX) lightLux = MAX_EXPECTED_LUX; 
  
  lightPercent = mapFloat(lightRaw, LDR_RAW_ADC_MAX, LDR_RAW_ADC_MIN, 0, 100); 
  lightPercent = constrain(lightPercent, 0, 100);
}

void sendSensorData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("SensorData: WiFi not connected.");
    return;
  }
  
  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + SENSOR_DATA_ENDPOINT_PATH;
  
  StaticJsonDocument<JSON_SENSOR_DATA_SIZE> jsonDoc;
  jsonDoc["tower_id"] = TOWER_ID;
  jsonDoc["sensor_value"] = lightRaw; 
  jsonDoc["light_level"] = lightPercent;
  jsonDoc["lux"] = lightLux; 
  jsonDoc["faulty_lights"] = faultUnderVoltage || faultOverVoltage || faultLowCurrent || faultHighCurrent || faultLowPower ? 1 : 0;
  jsonDoc["maintenance_lights"] = 0; 
  jsonDoc["efficiency"] = relayState ? mapFloat(lightPercent, 0, 100, 50, 95) : 0; 
  jsonDoc["voltage"] = voltage;
  jsonDoc["current"] = current;
  jsonDoc["power"] = power;
  
  String jsonString;
  serializeJson(jsonDoc, jsonString);
  
  int httpResponseCode = 0;
  for (int retry = 0; retry < MAX_HTTP_RETRIES; retry++) {
    http.begin(fullUrl); 
    http.addHeader("Content-Type", "application/json");
    httpResponseCode = http.POST(jsonString);
    if (httpResponseCode > 0) {
      break; 
    } else {
      Serial.printf("SensorData: HTTP POST failed, error: %s. Retry %d/%d\n", http.errorToString(httpResponseCode).c_str(), retry + 1, MAX_HTTP_RETRIES);
      Serial.flush();
      delay(HTTP_RETRY_DELAY_MS);
    }
    http.end(); 
  }
  
  if (httpResponseCode > 0) {
    Serial.printf("Sensor data sent (HTTP %d)\n", httpResponseCode);
    blinkLED(1, 50); 
  } else {
    Serial.printf("Sensor data send FAILED (HTTP %d)\n", httpResponseCode);
  }
  Serial.flush();
  if(http.connected()) http.end();
}

void updateStatus() {
  if (manualOverride && (millis() - manualOverrideStartTime > MANUAL_CONTROL_TIMEOUT_MS)) {
    manualOverride = false;
    Serial.println("Manual override timed out.");
    Serial.flush();
  }

  struct tm timeinfo;
  if (!getLocalTime(&timeinfo, 1000)) { 
    Serial.println("Failed to obtain time (getLocalTime).");
    Serial.flush();
  }
  
  bool isNight = false;
  int currentMinuteOfDay = timeinfo.tm_hour * 60 + timeinfo.tm_min;
  int nightStartMinuteOfDay = NIGHT_HOUR_START * 60 + NIGHT_MINUTE_START;
  int nightEndMinuteOfDay = NIGHT_HOUR_END * 60 + NIGHT_MINUTE_END;

  if (nightStartMinuteOfDay > nightEndMinuteOfDay) { 
    if (currentMinuteOfDay >= nightStartMinuteOfDay || currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  } else { 
    if (currentMinuteOfDay >= nightStartMinuteOfDay && currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  }
  
  bool isLowLight = (lightPercent < LIGHT_PERCENT_DARK_THRESHOLD); 
                
  bool shouldBeOn = false;
  if (manualOverride) {
    shouldBeOn = relayState; 
  } else {
    if (isNight || isLowLight) {
      shouldBeOn = true;
    } else {
      shouldBeOn = false;
    }
  }
  
  if (shouldBeOn != relayState) {
    controlRelay(shouldBeOn);
  }
  
  String newStatus = currentStatus;
  String newFaultType = faultType;
  
  if (faultUnderVoltage || faultOverVoltage || faultLowCurrent || faultHighCurrent || faultLowPower) {
    newStatus = "faulty";
    if (faultUnderVoltage) newFaultType = "undervoltage";
    else if (faultOverVoltage) newFaultType = "overvoltage";
    else if (faultLowCurrent && voltage > VOLTAGE_LOW_THRESHOLD) newFaultType = "bulb_or_wiring"; 
    else if (faultHighCurrent) newFaultType = "overcurrent_or_short";
    else if (faultLowPower && voltage > VOLTAGE_LOW_THRESHOLD && current > CURRENT_LOW_THRESHOLD / 2.0) newFaultType = "connection_issue";
    else newFaultType = "unknown_electrical"; 
  } else if (relayState) {
    newStatus = "active";
    newFaultType = "";
  } else {
    newStatus = "inactive";
    newFaultType = "";
  }
  
  if (newStatus != currentStatus || newFaultType != faultType) {
    Serial.printf("Status: %s (Fault: %s) -> %s (Fault: %s)\n", 
                  currentStatus.c_str(), faultType.c_str(), 
                  newStatus.c_str(), newFaultType.c_str());
    Serial.flush();
    currentStatus = newStatus;
    faultType = newFaultType;
  }
}

void sendStatusUpdate() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("StatusUpdate: WiFi not connected.");
    return;
  }
  
  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + STATUS_UPDATE_ENDPOINT_PATH;
  
  StaticJsonDocument<JSON_STATUS_UPDATE_SIZE> jsonDoc; 
  jsonDoc["tower_id"] = TOWER_ID;
  jsonDoc["status"] = currentStatus;
  if (currentStatus == "faulty" && faultType.length() > 0) {
    jsonDoc["fault_type"] = faultType;
  }

  String jsonString;
  serializeJson(jsonDoc, jsonString);
  
  int httpResponseCode = 0;
  for (int retry = 0; retry < MAX_HTTP_RETRIES; retry++) {
    http.begin(fullUrl); 
    http.addHeader("Content-Type", "application/json");
    httpResponseCode = http.POST(jsonString);
    if (httpResponseCode > 0) {
      break; 
    } else {
      Serial.printf("StatusUpdate: HTTP POST failed, error: %s. Retry %d/%d\n", http.errorToString(httpResponseCode).c_str(), retry + 1, MAX_HTTP_RETRIES);
      Serial.flush();
      delay(HTTP_RETRY_DELAY_MS);
    }
    http.end(); 
  }

  if (httpResponseCode > 0) {
    Serial.printf("Status update sent (HTTP %d)\n", httpResponseCode);
  } else {
    Serial.printf("Status update FAILED (HTTP %d)\n", httpResponseCode);
  }
  Serial.flush();
  if(http.connected()) http.end();
}

void controlRelay(bool state) {
  digitalWrite(RELAY_PIN, state ? HIGH : LOW);
  if (relayState != state) {
    relayState = state;
    Serial.printf("Relay state: %s\n", state ? "ON" : "OFF");
    Serial.flush();
  }
}

void checkFaults() {
  faultUnderVoltage = (voltage < VOLTAGE_LOW_THRESHOLD && voltage > 0.5); 
  faultOverVoltage = (voltage > VOLTAGE_HIGH_THRESHOLD);
  faultLowCurrent = (relayState && voltage > VOLTAGE_LOW_THRESHOLD && current < CURRENT_LOW_THRESHOLD && current >= 0);
  faultHighCurrent = (current > CURRENT_HIGH_THRESHOLD);
  faultLowPower = (relayState && voltage > VOLTAGE_LOW_THRESHOLD && current > (CURRENT_LOW_THRESHOLD / 2.0) && power < POWER_LOW_THRESHOLD);
}

void displayStatus() {
  Serial.println("--- Status ---");
  Serial.printf("V:%.2f I:%.3f P:%.2f | LDR:%d Lux:%.1f Light%%:%d\n", 
                voltage, current, power, lightRaw, lightLux, lightPercent);
  Serial.printf("State: %s (Fault:%s) Relay:%s Manual:%s",
                currentStatus.c_str(), faultType.c_str(), relayState ? "ON" : "OFF", manualOverride ? "Active" : "Off");
  if(manualOverride) Serial.printf(" (%.1fm left)", (MANUAL_CONTROL_TIMEOUT_MS - (millis() - manualOverrideStartTime))/60000.0f);
  Serial.println();
  Serial.println("--------------");
  Serial.flush();
}

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(STATUS_LED_PIN, HIGH); 
    delay(delayMs);
    digitalWrite(STATUS_LED_PIN, LOW);
    if (i < times -1 ) delay(delayMs); 
  }
}

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  if (in_max == in_min) return out_min; 
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void setManualOverride(bool manualState, bool newRelayState) {
    manualOverride = manualState;
    if (manualOverride) {
        manualOverrideStartTime = millis();
        Serial.printf("Manual override activated. Target relay state: %s.\n", newRelayState ? "ON" : "OFF");
        Serial.flush();
        controlRelay(newRelayState); 
    } else {
        Serial.println("Manual override deactivated. Returning to automatic control.");
        Serial.flush();
    }
}

