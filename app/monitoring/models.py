from django.db import models
from django.contrib.auth.models import User
from django.core.validators import MinValueValidator, MaxValueValidator
from django.utils import timezone
from datetime import timedelta

class TowerLight(models.Model):
    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('faulty', 'Faulty'),
        ('maintenance', 'Under Maintenance')
    ]

    name = models.CharField(max_length=100)
    latitude = models.FloatField(
        validators=[MinValueValidator(-90), MaxValueValidator(90)]
    )
    longitude = models.FloatField(
        validators=[<PERSON>V<PERSON>ueValidator(-180), MaxValueValidator(180)]
    )
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='inactive')
    last_checked = models.DateTimeField(auto_now=True)
    installation_date = models.DateField()
    description = models.TextField(blank=True)

    def __str__(self):
        return f"{self.name} - {self.status}"

    @classmethod
    def get_status_counts(cls):
        return cls.objects.values('status').annotate(count=models.Count('id'))

    @classmethod
    def calculate_efficiency(cls):
        total_towers = cls.objects.count()
        active_towers = cls.objects.filter(status='active').count()
        return (active_towers / total_towers) * 100 if total_towers > 0 else 0

class FaultLog(models.Model):
    FAULT_TYPES = [
        ('power', 'Power Failure'),
        ('bulb', 'Bulb Failure'),
        ('connection', 'Connection Issue'),
        ('other', 'Other')
    ]

    tower = models.ForeignKey(TowerLight, on_delete=models.CASCADE, related_name='faults')
    fault_type = models.CharField(max_length=20, choices=FAULT_TYPES)
    description = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    resolved = models.BooleanField(default=False)
    resolved_at = models.DateTimeField(null=True, blank=True)
    resolved_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='resolved_faults'
    )

    class Meta:
        ordering = ['-timestamp']

    def __str__(self):
        return f"{self.tower.name} - {self.fault_type} - {self.timestamp}"

    @classmethod
    def get_recent_alerts(cls, limit=10):
        return cls.objects.select_related('tower').filter(resolved=False)[:limit]

    @classmethod
    def calculate_weekly_change(cls):
        now = timezone.now()
        week_ago = now - timedelta(days=7)

        current_faults = cls.objects.filter(timestamp__gte=week_ago, resolved=False).count()
        previous_faults = cls.objects.filter(timestamp__lt=week_ago, timestamp__gte=week_ago - timedelta(days=7), resolved=False).count()

        if previous_faults > 0:
            change = ((current_faults - previous_faults) / previous_faults) * 100
        else:
            change = 100 if current_faults > 0 else 0

        return change

class Technician(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    phone_number = models.CharField(max_length=15)
    region = models.CharField(max_length=100)
    is_supervisor = models.BooleanField(default=False)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.region}"

class MaintenanceSchedule(models.Model):
    tower = models.ForeignKey(TowerLight, on_delete=models.CASCADE, related_name='maintenance_schedules')
    technician = models.ForeignKey(Technician, on_delete=models.CASCADE, related_name='scheduled_maintenance')
    scheduled_date = models.DateTimeField()
    completed = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    class Meta:
        ordering = ['scheduled_date']

    def __str__(self):
        return f"{self.tower.name} - {self.scheduled_date}"

    @classmethod
    def get_upcoming_maintenance(cls, limit=5):
        return cls.objects.select_related('tower', 'technician').filter(
            completed=False,
            scheduled_date__gte=timezone.now()
        )[:limit]

    @classmethod
    def calculate_weekly_change(cls):
        now = timezone.now()
        week_ago = now - timedelta(days=7)

        current_scheduled = cls.objects.filter(scheduled_date__gte=now, completed=False).count()
        previous_scheduled = cls.objects.filter(scheduled_date__lt=now, scheduled_date__gte=week_ago, completed=False).count()

        if previous_scheduled > 0:
            change = ((current_scheduled - previous_scheduled) / previous_scheduled) * 100
        else:
            change = 100 if current_scheduled > 0 else 0

        return change

class SensorData(models.Model):
    tower = models.ForeignKey(TowerLight, on_delete=models.CASCADE, related_name='sensor_data')
    timestamp = models.DateTimeField(auto_now_add=True)
    sensor_value = models.IntegerField(help_text="Raw sensor value")
    light_level = models.IntegerField(validators=[MinValueValidator(0), MaxValueValidator(100)],
                                    help_text="Light level percentage (0-100)")
    faulty_lights = models.IntegerField(default=0, help_text="Number of faulty lights detected")
    maintenance_lights = models.IntegerField(default=0, help_text="Number of lights needing maintenance")
    efficiency = models.FloatField(validators=[MinValueValidator(0), MaxValueValidator(100)],
                                 help_text="Efficiency percentage (0-100)")
    voltage = models.FloatField(null=True, blank=True, help_text="Voltage in volts")
    current = models.FloatField(null=True, blank=True, help_text="Current in amperes")
    power = models.FloatField(null=True, blank=True, help_text="Power in watts")

    class Meta:
        ordering = ['-timestamp']
        verbose_name_plural = "Sensor Data"

    def __str__(self):
        return f"{self.tower.name} - {self.timestamp}"

    @classmethod
    def get_latest_data(cls, tower_id=None, limit=1):
        """Get the latest sensor data for a specific tower or all towers"""
        queryset = cls.objects.select_related('tower')
        if tower_id:
            queryset = queryset.filter(tower_id=tower_id)
        return queryset.order_by('-timestamp')[:limit]

class TowerCommand(models.Model):
    """Model to store commands to be sent to microcontrollers"""
    COMMAND_TYPES = [
        ('turn_on', 'Turn On'),
        ('turn_off', 'Turn Off'),
        ('maintenance_mode', 'Maintenance Mode'),
        ('reset', 'Reset'),
        ('manual_override_on', 'Manual Override On'),
        ('manual_override_off', 'Manual Override Off'),
        ('get_status', 'Get Status'),
    ]

    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('sent', 'Sent'),
        ('acknowledged', 'Acknowledged'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
        ('expired', 'Expired'),
    ]

    tower = models.ForeignKey(TowerLight, on_delete=models.CASCADE, related_name='commands')
    command_type = models.CharField(max_length=20, choices=COMMAND_TYPES)
    parameters = models.JSONField(default=dict, blank=True, help_text="Additional command parameters")
    status = models.CharField(max_length=15, choices=STATUS_CHOICES, default='pending')
    created_at = models.DateTimeField(auto_now_add=True)
    sent_at = models.DateTimeField(null=True, blank=True)
    acknowledged_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)
    expires_at = models.DateTimeField(help_text="Command expires after this time")
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='tower_commands'
    )
    response_data = models.JSONField(default=dict, blank=True, help_text="Response from microcontroller")
    error_message = models.TextField(blank=True, help_text="Error message if command failed")

    class Meta:
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['tower', 'status']),
            models.Index(fields=['status', 'expires_at']),
        ]

    def __str__(self):
        return f"{self.tower.name} - {self.command_type} ({self.status})"

    @classmethod
    def get_pending_commands(cls, tower_id):
        """Get pending commands for a specific tower"""
        return cls.objects.filter(
            tower_id=tower_id,
            status='pending',
            expires_at__gt=timezone.now()
        ).order_by('created_at')

    @classmethod
    def cleanup_expired_commands(cls):
        """Mark expired commands as expired"""
        expired_count = cls.objects.filter(
            status__in=['pending', 'sent'],
            expires_at__lte=timezone.now()
        ).update(status='expired')
        return expired_count

    def mark_sent(self):
        """Mark command as sent"""
        self.status = 'sent'
        self.sent_at = timezone.now()
        self.save()

    def mark_acknowledged(self):
        """Mark command as acknowledged by microcontroller"""
        self.status = 'acknowledged'
        self.acknowledged_at = timezone.now()
        self.save()

    def mark_completed(self, response_data=None):
        """Mark command as completed"""
        self.status = 'completed'
        self.completed_at = timezone.now()
        if response_data:
            self.response_data = response_data
        self.save()

    def mark_failed(self, error_message):
        """Mark command as failed"""
        self.status = 'failed'
        self.error_message = error_message
        self.save()
