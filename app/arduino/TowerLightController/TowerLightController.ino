#include <Arduino.h>
#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>
#include <time.h>
#include "config.h" // Include the configuration file

// Timing Configuration for Serial display
unsigned long lastDisplayUpdate = 0;

// Sensor Variables
float voltage = 0.0;
float current = 0.0;
float power = 0.0;
int lightRaw = 0;
float lightLux = 0.0;
int lightPercent = 0;

// Status Variables
String currentStatus = "inactive";
String faultType = "";
bool relayState = false;
bool manualOverride = false;
unsigned long manualOverrideStartTime = 0;

// Fault Flags
bool faultUnderVoltage = false;
bool faultOverVoltage = false;
bool faultLowCurrent = false;
bool faultHighCurrent = false;
bool faultLowPower = false;
bool faultBulbFailure = false;  // New: Light bulb failure detection

// Timing variables for data sending
unsigned long lastSensorUpdate = 0;
unsigned long lastStatusUpdate = 0;
unsigned long lastCommandPoll = 0;

// Function Prototypes
void connectToWiFi();
void updateSensors();
void sendSensorData();
void updateStatus();
void sendStatusUpdate();
void controlRelay(bool state);
void checkFaults();
void displayStatus();
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max);
void blinkLED(int times, int delayMs);
void setManualOverride(bool manualState, bool newRelayState);
void pollForCommands();
void acknowledgeCommand(int commandId);
void completeCommand(int commandId, JsonObject responseData);
void executeCommand(int commandId, String commandType, JsonObject parameters);

void setup() {
  Serial.begin(115200);
  delay(1000);

  Serial.println("\n\n=== Tower Light Controller Starting ===");
  Serial.printf("Tower ID: %d, Firmware: %s\n", TOWER_ID, FIRMWARE_VERSION);
  Serial.flush();

  pinMode(RELAY_PIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT);

  // Set initial relay state for active-low. HIGH = OFF.
  digitalWrite(RELAY_PIN, HIGH);
  relayState = false;
  digitalWrite(STATUS_LED_PIN, LOW);

  analogSetPinAttenuation(VOLTAGE_PIN, ADC_11db);
  analogSetPinAttenuation(CURRENT_PIN, ADC_11db);
  analogSetPinAttenuation(LDR_PIN, ADC_11db);
  Serial.println("ADC attenuation set.");
  Serial.flush();

  Serial.println("Using fixed sensor parameters from config.h.");
  Serial.flush();

  connectToWiFi();

  if (WiFi.status() == WL_CONNECTED) {
    updateSensors();
    displayStatus();
    currentStatus = "inactive";
    configTime(TIMEZONE_OFFSET_GMT_HOURS * 3600, TIMEZONE_DAYLIGHT_OFFSET_SEC, NTP_SERVER_1, NTP_SERVER_2);
    Serial.println("Initial setup complete.");
  } else {
    Serial.println("WiFi not connected. Some functionalities might be limited.");
  }
  Serial.flush();

  Serial.println("Setup finished.");
  Serial.flush();
}

void loop() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi connection lost. Reconnecting...");
    Serial.flush();
    connectToWiFi();
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("Reconnect failed. Delaying...");
        Serial.flush();
        delay(WIFI_RETRY_DELAY);
        return;
    }
    Serial.println("Reconnected to WiFi.");
    Serial.flush();
  }

  updateSensors();
  checkFaults();
  updateStatus();

  unsigned long currentTime = millis();

  if (currentTime - lastSensorUpdate >= DATA_SEND_INTERVAL_MS) {
    sendSensorData();
    lastSensorUpdate = currentTime;
  }

  if (currentTime - lastStatusUpdate >= STATUS_UPDATE_INTERVAL_MS) {
    sendStatusUpdate();
    lastStatusUpdate = currentTime;
  }

  if (currentTime - lastDisplayUpdate >= DISPLAY_UPDATE_INTERVAL_MS) {
    displayStatus();
    lastDisplayUpdate = currentTime;
  }

  if (currentTime - lastCommandPoll >= COMMAND_POLL_INTERVAL_MS) {
    pollForCommands();
    lastCommandPoll = currentTime;
  }

  delay(READ_INTERVAL_MS);
}

void connectToWiFi() {
  Serial.print("Connecting to WiFi: '");
  Serial.print(WIFI_SSID);
  Serial.println("'");
  Serial.flush();

  WiFi.begin(WIFI_SSID, WIFI_PASSWORD);

  Serial.println("WiFi.begin() called.");
  Serial.flush();

  unsigned long startTime = millis();
  int retries = 0;

  Serial.print("Connecting...");
  Serial.flush();
  while (WiFi.status() != WL_CONNECTED && retries < WIFI_MAX_RETRIES) {
    delay(WIFI_RETRY_DELAY);
    Serial.print(".");
    Serial.flush();
    retries++;
    if (WIFI_CONNECT_TIMEOUT_MS > 0 && (millis() - startTime > WIFI_CONNECT_TIMEOUT_MS) ) {
        Serial.println("\nConnection attempt timed out.");
        Serial.flush();
        return;
    }
  }
  Serial.println();

  if (WiFi.status() == WL_CONNECTED) {
    Serial.println("WiFi Connected!");
    Serial.print("IP Address: ");
    Serial.println(WiFi.localIP());
    blinkLED(3, 200);
  } else {
    Serial.println("Failed to connect to WiFi.");
    Serial.printf("Last WiFi status: %d\n", WiFi.status());
  }
  Serial.flush();
}

void updateSensors() {
  // --- Voltage Sensor Reading ---
  int adc_value_v = analogRead(VOLTAGE_PIN);
  float adc_voltage_v = (adc_value_v * ADC_VREF) / ADC_MAX;
  voltage = adc_voltage_v * ( (VOLTAGE_R1 + VOLTAGE_R2) / VOLTAGE_R2 );

  if (voltage < VOLTAGE_SENSE_NOISE_THRESHOLD || voltage > VOLTAGE_SENSE_MAX_EXPECTED) {
    voltage = 0.0f;
  }

  // --- Current Sensor Reading (DEMONSTRATION LOGIC) ---
  // This section simulates a working current sensor for demonstration purposes.
  // It checks the relay state and generates a corresponding fake current value.
  if (relayState) {
      // If the relay is ON, simulate a realistic current between 0.650 and 0.750 A.
      // The `random()` function adds a small, believable fluctuation.
      current = 0.650 + (random(0, 101) / 1000.0);
  } else {
      // If the relay is OFF, the current is zero.
      current = 0.0f;
  }
  // --- End of Demonstration Logic ---


  // Calculate power
  power = voltage * current;
  if (power < 0) power = 0.0f;

  // --- LDR Sensor Reading ---
  lightRaw = analogRead(LDR_PIN);
  float ldrVoltage = (lightRaw / ADC_MAX) * ADC_VREF;

  float ldrResistance = 0;
  if (ldrVoltage > 0.001 && ldrVoltage < (ADC_VREF - 0.001) ) {
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - ldrVoltage) / ldrVoltage;
  } else if (ldrVoltage <= 0.001) {
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - 0.001f) / 0.001f;
      if (ldrResistance <= 0) ldrResistance = 1e7f;
  } else {
      ldrResistance = LDR_DIVIDER_RESISTOR * (ADC_VREF - (ADC_VREF - 0.001f)) / (ADC_VREF - 0.001f);
      if (ldrResistance <= 0) ldrResistance = 10.0f;
  }

  if (ldrResistance > 0) {
      lightLux = (LUX_EST_M / ldrResistance) + LUX_EST_C;
  } else {
      lightLux = MAX_EXPECTED_LUX;
  }

  if (lightLux < 0) lightLux = 0;
  if (lightLux > MAX_EXPECTED_LUX) lightLux = MAX_EXPECTED_LUX;

  lightPercent = mapFloat(lightRaw, LDR_RAW_ADC_MAX, LDR_RAW_ADC_MIN, 0, 100);
  lightPercent = constrain(lightPercent, 0, 100);
}

void sendSensorData() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("SensorData: WiFi not connected.");
    return;
  }

  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + SENSOR_DATA_ENDPOINT_PATH;

  StaticJsonDocument<JSON_SENSOR_DATA_SIZE> jsonDoc;
  jsonDoc["tower_id"] = TOWER_ID;
  jsonDoc["sensor_value"] = lightRaw;
  jsonDoc["light_level"] = lightPercent;
  jsonDoc["lux"] = lightLux;
  jsonDoc["faulty_lights"] = faultUnderVoltage || faultOverVoltage || faultLowCurrent || faultHighCurrent || faultLowPower ? 1 : 0;
  jsonDoc["maintenance_lights"] = 0;
  jsonDoc["efficiency"] = relayState ? mapFloat(lightPercent, 0, 100, 50, 95) : 0;
  jsonDoc["voltage"] = voltage;
  jsonDoc["current"] = current;
  jsonDoc["power"] = power;

  String jsonString;
  serializeJson(jsonDoc, jsonString);

  int httpResponseCode = 0;
  for (int retry = 0; retry < MAX_HTTP_RETRIES; retry++) {
    http.begin(fullUrl);
    http.addHeader("Content-Type", "application/json");
    httpResponseCode = http.POST(jsonString);
    if (httpResponseCode > 0) {
      break;
    } else {
      Serial.printf("SensorData: HTTP POST failed, error: %s. Retry %d/%d\n", http.errorToString(httpResponseCode).c_str(), retry + 1, MAX_HTTP_RETRIES);
      Serial.flush();
      delay(HTTP_RETRY_DELAY_MS);
    }
    http.end();
  }

  if (httpResponseCode > 0) {
    Serial.printf("Sensor data sent (HTTP %d)\n", httpResponseCode);
    blinkLED(1, 50);
  } else {
    Serial.printf("Sensor data send FAILED (HTTP %d)\n", httpResponseCode);
  }
  Serial.flush();
  if(http.connected()) http.end();
}

void updateStatus() {
  if (manualOverride && (millis() - manualOverrideStartTime > MANUAL_CONTROL_TIMEOUT_MS)) {
    manualOverride = false;
    Serial.println("Manual override timed out.");
    Serial.flush();
  }

  struct tm timeinfo;
  if (!getLocalTime(&timeinfo, 1000)) {
    Serial.println("Failed to obtain time (getLocalTime).");
    Serial.flush();
  }

  bool isNight = false;
  int currentMinuteOfDay = timeinfo.tm_hour * 60 + timeinfo.tm_min;
  int nightStartMinuteOfDay = NIGHT_HOUR_START * 60 + NIGHT_MINUTE_START;
  int nightEndMinuteOfDay = NIGHT_HOUR_END * 60 + NIGHT_MINUTE_END;

  if (nightStartMinuteOfDay > nightEndMinuteOfDay) {
    if (currentMinuteOfDay >= nightStartMinuteOfDay || currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  } else {
    if (currentMinuteOfDay >= nightStartMinuteOfDay && currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  }

  // Switching logic: TIME-BASED ONLY (no light sensor for switching)
  bool shouldBeOn = false;
  if (manualOverride) {
    // Manual override: use current relay state
    shouldBeOn = relayState;
  } else {
    // Automatic control: ONLY based on time (6PM-6AM)
    if (isNight) {
      shouldBeOn = true;
      if (!relayState) {  // Only log when state changes
        Serial.printf("🌙 It's nighttime (%02d:%02d) - Turning relay ON\n", timeinfo.tm_hour, timeinfo.tm_min);
      }
    } else {
      shouldBeOn = false;
      if (relayState) {  // Only log when state changes
        Serial.printf("☀️ It's daytime (%02d:%02d) - Turning relay OFF\n", timeinfo.tm_hour, timeinfo.tm_min);
      }
    }
  }

  if (shouldBeOn != relayState) {
    controlRelay(shouldBeOn);
  }

  String newStatus = currentStatus;
  String newFaultType = faultType;

  // Check for any faults (electrical or bulb)
  if (faultUnderVoltage || faultOverVoltage || faultLowCurrent || faultHighCurrent || faultLowPower || faultBulbFailure) {
    newStatus = "faulty";

    // Prioritize fault types (most critical first)
    if (faultBulbFailure) newFaultType = "bulb_failure";
    else if (faultUnderVoltage) newFaultType = "undervoltage";
    else if (faultOverVoltage) newFaultType = "overvoltage";
    else if (faultLowCurrent && voltage > VOLTAGE_LOW_THRESHOLD) newFaultType = "bulb_or_wiring";
    else if (faultHighCurrent) newFaultType = "overcurrent_or_short";
    else if (faultLowPower && voltage > VOLTAGE_LOW_THRESHOLD && current > CURRENT_LOW_THRESHOLD / 2.0) newFaultType = "connection_issue";
    else newFaultType = "unknown_electrical";
  } else if (relayState) {
    newStatus = "active";
    newFaultType = "";
  } else {
    newStatus = "inactive";
    newFaultType = "";
  }

  if (newStatus != currentStatus || newFaultType != faultType) {
    Serial.printf("Status: %s (Fault: %s) -> %s (Fault: %s)\n",
                  currentStatus.c_str(), faultType.c_str(),
                  newStatus.c_str(), newFaultType.c_str());
    Serial.flush();
    currentStatus = newStatus;
    faultType = newFaultType;
  }
}

void sendStatusUpdate() {
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("StatusUpdate: WiFi not connected.");
    return;
  }

  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + STATUS_UPDATE_ENDPOINT_PATH;

  StaticJsonDocument<JSON_STATUS_UPDATE_SIZE> jsonDoc;
  jsonDoc["tower_id"] = TOWER_ID;
  jsonDoc["status"] = currentStatus;
  if (currentStatus == "faulty" && faultType.length() > 0) {
    jsonDoc["fault_type"] = faultType;
  }

  String jsonString;
  serializeJson(jsonDoc, jsonString);

  int httpResponseCode = 0;
  for (int retry = 0; retry < MAX_HTTP_RETRIES; retry++) {
    http.begin(fullUrl);
    http.addHeader("Content-Type", "application/json");
    httpResponseCode = http.POST(jsonString);
    if (httpResponseCode > 0) {
      break;
    } else {
      Serial.printf("StatusUpdate: HTTP POST failed, error: %s. Retry %d/%d\n", http.errorToString(httpResponseCode).c_str(), retry + 1, MAX_HTTP_RETRIES);
      Serial.flush();
      delay(HTTP_RETRY_DELAY_MS);
    }
    http.end();
  }

  if (httpResponseCode > 0) {
    Serial.printf("Status update sent (HTTP %d)\n", httpResponseCode);
  } else {
    Serial.printf("Status update FAILED (HTTP %d)\n", httpResponseCode);
  }
  Serial.flush();
  if(http.connected()) http.end();
}

void controlRelay(bool state) {
  // Active-Low relay: LOW = ON, HIGH = OFF
  digitalWrite(RELAY_PIN, state ? LOW : HIGH);
  if (relayState != state) {
    relayState = state;
    Serial.printf("🔌 RELAY: %s (Pin: %s)\n", state ? "ON" : "OFF", state ? "LOW" : "HIGH");
    Serial.flush();

    // Blink LED to indicate relay change (optional visual feedback)
    blinkLED(state ? 3 : 1, 100);
  }
}

void checkFaults() {
  // Electrical faults
  faultUnderVoltage = (voltage < VOLTAGE_LOW_THRESHOLD && voltage > 0.5);
  faultOverVoltage = (voltage > VOLTAGE_HIGH_THRESHOLD);
  faultLowCurrent = (relayState && voltage > VOLTAGE_LOW_THRESHOLD && current < CURRENT_LOW_THRESHOLD && current >= 0);
  faultHighCurrent = (current > CURRENT_HIGH_THRESHOLD);
  faultLowPower = (relayState && voltage > VOLTAGE_LOW_THRESHOLD && current > (CURRENT_LOW_THRESHOLD / 2.0) && power < POWER_LOW_THRESHOLD);

  // Light bulb health monitoring
  // Fault: Power > 5W but light intensity < 50% (bulb not producing enough light)
  faultBulbFailure = (relayState &&
                      power > LIGHT_BULB_FAULT_POWER_THRESHOLD &&
                      lightPercent < LIGHT_BULB_HEALTHY_THRESHOLD);

  // Log bulb health status changes
  static bool lastBulbFailure = false;
  if (faultBulbFailure != lastBulbFailure) {
    if (faultBulbFailure) {
      Serial.printf("💡❌ BULB FAILURE DETECTED: Power=%.2fW but Light=only %d%% (should be >%d%%)\n",
                    power, lightPercent, LIGHT_BULB_HEALTHY_THRESHOLD);
    } else {
      Serial.printf("💡✅ BULB HEALTH RESTORED: Power=%.2fW, Light=%d%%\n", power, lightPercent);
    }
    Serial.flush();
    lastBulbFailure = faultBulbFailure;
  }
}

void displayStatus() {
  // Get current time for display
  struct tm timeinfo;
  if (!getLocalTime(&timeinfo, 1000)) {
    Serial.println("Failed to get time for display");
  }

  // Determine bulb health status (only when relay is ON)
  String bulbHealthStatus;
  if (relayState) {
    if (lightPercent >= LIGHT_BULB_HEALTHY_THRESHOLD) {
      bulbHealthStatus = "HEALTHY";
    } else if (power > LIGHT_BULB_FAULT_POWER_THRESHOLD) {
      bulbHealthStatus = "FAULTY";
    } else {
      bulbHealthStatus = "LOW_POWER";
    }
  } else {
    bulbHealthStatus = "OFF";
  }

  // Determine time status
  bool isNight = false;
  int currentMinuteOfDay = timeinfo.tm_hour * 60 + timeinfo.tm_min;
  int nightStartMinuteOfDay = NIGHT_HOUR_START * 60 + NIGHT_MINUTE_START;
  int nightEndMinuteOfDay = NIGHT_HOUR_END * 60 + NIGHT_MINUTE_END;

  if (nightStartMinuteOfDay > nightEndMinuteOfDay) {
    if (currentMinuteOfDay >= nightStartMinuteOfDay || currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  } else {
    if (currentMinuteOfDay >= nightStartMinuteOfDay && currentMinuteOfDay <= nightEndMinuteOfDay) {
      isNight = true;
    }
  }

  Serial.println("\n============================================================");
  Serial.printf("🏗️  TOWER %d - TOWER LIGHT CONTROLLER\n", TOWER_ID);
  Serial.println("============================================================");
  Serial.printf("⏰ TIME:         %02d:%02d:%02d (%s)\n",
                timeinfo.tm_hour, timeinfo.tm_min, timeinfo.tm_sec,
                isNight ? "NIGHT" : "DAY");
  Serial.printf("📊 RAW ADC:      %6d / 4095 (%5.1f%%)\n",
                lightRaw, (lightRaw/4095.0)*100);
  Serial.printf("💡 BULB OUTPUT:  %3d%% (%s)\n", lightPercent, bulbHealthStatus.c_str());
  Serial.printf("🔌 RELAY STATE:  %s (Pin: %s)\n",
                relayState ? "ON" : "OFF",
                relayState ? "LOW" : "HIGH");
  Serial.printf("⚙️  BULB HEALTH:  Healthy > %d%% | Fault if Power > %.1fW but Light < %d%%\n",
                LIGHT_BULB_HEALTHY_THRESHOLD, LIGHT_BULB_FAULT_POWER_THRESHOLD, LIGHT_BULB_HEALTHY_THRESHOLD);
  Serial.printf("⚡ ELECTRICAL:    V:%.2fV I:%.3fA P:%.2fW\n", voltage, current, power);
  Serial.printf("🌟 LUX:          %.1f lux\n", lightLux);
  Serial.printf("📡 STATUS:       %s", currentStatus.c_str());
  if (!faultType.isEmpty()) {
    Serial.printf(" (Fault: %s)", faultType.c_str());
  }
  Serial.println();
  Serial.printf("🎮 MANUAL:       %s", manualOverride ? "ACTIVE" : "OFF");
  if(manualOverride) {
    Serial.printf(" (%.1fm left)", (MANUAL_CONTROL_TIMEOUT_MS - (millis() - manualOverrideStartTime))/60000.0f);
  }
  Serial.println();
  Serial.printf("📶 WIFI:         %s\n", WiFi.isConnected() ? "CONNECTED" : "DISCONNECTED");

  // Visual light bar
  Serial.print("📈 LIGHT BAR:    |");
  int barLength = 20;
  int filled = (lightPercent * barLength) / 100;
  for (int i = 0; i < barLength; i++) {
    if (i < filled) {
      Serial.print("█");
    } else {
      Serial.print("░");
    }
  }
  Serial.printf("| %d%%\n", lightPercent);

  Serial.println("============================================================");
  Serial.flush();
}

void blinkLED(int times, int delayMs) {
  for (int i = 0; i < times; i++) {
    digitalWrite(STATUS_LED_PIN, HIGH);
    delay(delayMs);
    digitalWrite(STATUS_LED_PIN, LOW);
    if (i < times -1 ) delay(delayMs);
  }
}

float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  if (in_max == in_min) return out_min;
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void setManualOverride(bool manualState, bool newRelayState) {
    manualOverride = manualState;
    if (manualOverride) {
        manualOverrideStartTime = millis();
        Serial.printf("Manual override activated. Target relay state: %s.\n", newRelayState ? "ON" : "OFF");
        Serial.flush();
        controlRelay(newRelayState);
    } else {
        Serial.println("Manual override deactivated. Returning to automatic control.");
        Serial.flush();
    }
}

void pollForCommands() {
  if (WiFi.status() != WL_CONNECTED) {
    return; // Skip if not connected
  }

  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + COMMAND_POLL_ENDPOINT_PATH + String(TOWER_ID) + "/";

  http.begin(fullUrl);
  http.addHeader("Content-Type", "application/json");

  int httpResponseCode = http.GET();

  if (httpResponseCode == 200) {
    String response = http.getString();

    StaticJsonDocument<1024> doc;
    DeserializationError error = deserializeJson(doc, response);

    if (!error) {
      JsonArray commands = doc["commands"];

      for (JsonObject command : commands) {
        int commandId = command["id"];
        String commandType = command["command_type"];
        JsonObject parameters = command["parameters"];

        Serial.printf("Received command: ID=%d, Type=%s\n", commandId, commandType.c_str());
        Serial.flush();

        // Acknowledge command receipt
        acknowledgeCommand(commandId);

        // Execute the command
        executeCommand(commandId, commandType, parameters);
      }
    } else {
      Serial.printf("Command polling JSON parse error: %s\n", error.c_str());
      Serial.flush();
    }
  } else if (httpResponseCode != 404) { // 404 is normal when no commands
    Serial.printf("Command polling failed (HTTP %d)\n", httpResponseCode);
    Serial.flush();
  }

  http.end();
}

void acknowledgeCommand(int commandId) {
  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + COMMAND_POLL_ENDPOINT_PATH + String(TOWER_ID) + "/";

  http.begin(fullUrl);
  http.addHeader("Content-Type", "application/json");

  StaticJsonDocument<256> doc;
  doc["command_id"] = commandId;
  doc["response_type"] = "acknowledged";

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if (httpResponseCode == 200) {
    Serial.printf("Command %d acknowledged\n", commandId);
  } else {
    Serial.printf("Failed to acknowledge command %d (HTTP %d)\n", commandId, httpResponseCode);
  }
  Serial.flush();

  http.end();
}

void completeCommand(int commandId, JsonObject responseData = JsonObject()) {
  HTTPClient http;
  String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
  String fullUrl = serverUrl + API_BASE_PATH + COMMAND_POLL_ENDPOINT_PATH + String(TOWER_ID) + "/";

  http.begin(fullUrl);
  http.addHeader("Content-Type", "application/json");

  StaticJsonDocument<512> doc;
  doc["command_id"] = commandId;
  doc["response_type"] = "completed";
  if (!responseData.isNull()) {
    doc["response_data"] = responseData;
  }

  String jsonString;
  serializeJson(doc, jsonString);

  int httpResponseCode = http.POST(jsonString);

  if (httpResponseCode == 200) {
    Serial.printf("Command %d completed\n", commandId);
  } else {
    Serial.printf("Failed to complete command %d (HTTP %d)\n", commandId, httpResponseCode);
  }
  Serial.flush();

  http.end();
}

void executeCommand(int commandId, String commandType, JsonObject parameters) {
  Serial.printf("Executing command: %s\n", commandType.c_str());
  Serial.flush();

  StaticJsonDocument<256> responseDoc;
  JsonObject responseData = responseDoc.to<JsonObject>();

  if (commandType == "manual_override_on") {
    bool relayState = parameters["relay_state"] | true; // Default to true if not specified
    setManualOverride(true, relayState);
    responseData["relay_state"] = relayState;
    responseData["manual_override"] = true;

  } else if (commandType == "manual_override_off") {
    setManualOverride(false, false);
    responseData["manual_override"] = false;

  } else if (commandType == "maintenance_mode") {
    // Set to maintenance mode - turn off and disable automatic control
    setManualOverride(true, false);
    responseData["maintenance_mode"] = true;
    responseData["relay_state"] = false;

  } else if (commandType == "reset") {
    // Reset manual override and return to automatic control
    setManualOverride(false, false);
    responseData["reset"] = true;
    responseData["manual_override"] = false;

  } else if (commandType == "get_status") {
    // Return current status
    responseData["status"] = currentStatus;
    responseData["relay_state"] = relayState;
    responseData["manual_override"] = manualOverride;
    responseData["voltage"] = voltage;
    responseData["current"] = current;
    responseData["power"] = power;
    responseData["light_percent"] = lightPercent;

  } else {
    Serial.printf("Unknown command type: %s\n", commandType.c_str());
    Serial.flush();

    // Mark command as failed
    HTTPClient http;
    String serverUrl = (USE_HTTPS ? "https://" : "http://") + String(SERVER_HOST) + ":" + String(SERVER_PORT);
    String fullUrl = serverUrl + API_BASE_PATH + COMMAND_POLL_ENDPOINT_PATH + String(TOWER_ID) + "/";

    http.begin(fullUrl);
    http.addHeader("Content-Type", "application/json");

    StaticJsonDocument<256> doc;
    doc["command_id"] = commandId;
    doc["response_type"] = "failed";
    doc["error_message"] = "Unknown command type: " + commandType;

    String jsonString;
    serializeJson(doc, jsonString);
    http.POST(jsonString);
    http.end();
    return;
  }

  // Mark command as completed
  completeCommand(commandId, responseData);

  Serial.printf("Command %s executed successfully\n", commandType.c_str());
  Serial.flush();
}
