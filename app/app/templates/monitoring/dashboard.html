{% extends 'base.html' %}

{% block title %}Dashboard - Tower Light Monitoring{% endblock %}

{% block extra_head %}
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.9.1/gsap.min.js"></script>
    <style>
        .gradient-card {
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .gradient-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.3);
        }
        .chart-container {
            position: relative;
            transition: transform 0.3s ease;
        }
        .chart-container:hover {
            transform: scale(1.02);
        }
        /* Dark theme for Leaflet map */
        .leaflet-tile {
            filter: brightness(0.6) invert(1) contrast(3) hue-rotate(200deg) saturate(0.3) brightness(0.7);
        }
        .leaflet-container {
            background: #303030;
        }
        .animate-pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        @keyframes pulse {
            0%, 100% {
                opacity: 1;
            }
            50% {
                opacity: .5;
            }
        }
        .tower-icon {
            width: 24px !important;
            height: 24px !important;
        }

        .tower-popup {
            font-family: 'Inter', sans-serif;
        }

        .tower-popup .leaflet-popup-content-wrapper {
            background: rgba(52, 55, 60, 0.95);
            color: white;
            border-radius: 8px;
            padding: 0;
        }

        .tower-popup .leaflet-popup-content {
            margin: 0;
            padding: 12px;
        }

        .tower-popup .leaflet-popup-tip {
            background: rgba(52, 55, 60, 0.95);
        }

        .tower-status {
            display: inline-block;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.875rem;
            margin-top: 4px;
        }

        .status-active { background: rgba(61, 143, 202, 0.2); color: #3D8FCA; }
        .status-faulty { background: rgba(255, 87, 77, 0.2); color: #FF574D; }
        .status-maintenance { background: rgba(255, 200, 92, 0.2); color: #FFC85C; }
        .status-badge {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-top: 4px;
        }
        .status-green { background: rgba(34,197,94,0.15); color: #22c55e; }
        .status-red { background: rgba(239,68,68,0.15); color: #ef4444; }
        .status-orange { background: rgba(251,191,36,0.15); color: #fbbf24; }
        .status-gray { background: rgba(156,163,175,0.15); color: #9ca3af; }
    </style>
{% endblock %}

{% block content %}
    <div class="space-y-6 animate-fade-in">
        <!-- Welcome Section -->
        <div class="flex justify-between items-center mb-8">
            <div>
                <h1 class="text-3xl font-bold text-white mb-2">Welcome back, {{ user.username }}</h1>
                <p class="text-gray-400">Here's what's happening with your towerlighting network</p>
            </div>
            <div class="text-right">
                <p class="text-lg text-gray-300">{{ current_time|date:"l, F j" }}</p>
                <p class="text-gray-400">{{ current_time|date:"g:i A" }}</p>
            </div>
        </div>

        <!-- Status Cards -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            <!-- Active Lights Card -->
            <div id="active-lights-card" class="gradient-card rounded-xl p-6 bg-gradient-to-br from-primary to-primary-dark transform transition-all duration-300 hover:scale-105 cursor-pointer">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-white/10 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <span class="text-xs font-semibold text-white/80 px-2 py-1 bg-white/10 rounded-full">Live</span>
                </div>
                <h3 class="text-4xl font-bold text-white mb-2">{{ active_count }}</h3>
                <p class="text-white/80">Active Lights</p>
                <div class="mt-4 flex items-center text-white/80">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
                    </svg>
                    <span class="text-sm">3.2% from last week</span>
                </div>
            </div>

            <!-- Faulty Lights Card -->
            <div id="faulty-lights-card" class="gradient-card rounded-xl p-6 bg-gradient-to-br from-secondary to-secondary-dark transform transition-all duration-300 hover:scale-105 cursor-pointer">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-white/10 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                    </div>
                    <span class="text-xs font-semibold text-white/80 px-2 py-1 bg-white/10 rounded-full">Alert</span>
                </div>
                <h3 class="text-4xl font-bold text-white mb-2">{{ faulty_count }}</h3>
                <p class="text-white/80">Faulty Lights</p>
                <div class="mt-4 flex items-center text-white/80">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                    </svg>
                    <span class="text-sm">{{ fault_change }}% from last week</span>
                </div>
            </div>

            <!-- Maintenance Card -->
            <div id="maintenance-card" class="gradient-card rounded-xl p-6 bg-gradient-to-br from-warning to-warning-dark transform transition-all duration-300 hover:scale-105 cursor-pointer">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-white/10 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <span class="text-xs font-semibold text-white/80 px-2 py-1 bg-white/10 rounded-full">Scheduled</span>
                </div>
                <h3 class="text-4xl font-bold text-white mb-2">{{ maintenance_count }}</h3>
                <p class="text-white/80">Scheduled Maintenance</p>
                <div class="mt-4 flex items-center text-white/80">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
                    </svg>
                    <span class="text-sm">{{ maintenance_change }}% from last week</span>
                </div>
            </div>

            <!-- Efficiency Card -->
            <div id="efficiency-card" class="gradient-card rounded-xl p-6 bg-gradient-to-br from-accent to-accent-dark transform transition-all duration-300 hover:scale-105">
                <div class="flex items-center justify-between mb-4">
                    <div class="p-2 bg-white/10 rounded-lg">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <span class="text-xs font-semibold text-white/80 px-2 py-1 bg-white/10 rounded-full">Performance</span>
                </div>
                <h3 class="text-4xl font-bold text-white mb-2">{{ efficiency }}%</h3>
                <p class="text-white/80">Network Efficiency</p>
                <div class="mt-4 flex items-center text-white/80">
                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"/>
                    </svg>
                    <span class="text-sm">0.5% improvement</span>
                </div>
            </div>
        </div>

        <!-- Charts and Map -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Activity Chart -->
            <div class="bg-surface rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">Lighting Activity</h3>
                    <div class="flex space-x-2">
                        <button class="px-3 py-1 text-sm text-gray-300 hover:text-white bg-accent rounded-lg transition-colors">Day</button>
                        <button class="px-3 py-1 text-sm text-gray-300 hover:text-white bg-accent rounded-lg transition-colors">Week</button>
                        <button class="px-3 py-1 text-sm text-white bg-primary rounded-lg">Month</button>
                    </div>
                </div>
                <div class="chart-container h-64">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>

            <!-- Map -->
            <div class="bg-surface rounded-xl p-6 shadow-lg">
                <div class="flex justify-between items-center mb-6">
                    <h3 class="text-lg font-semibold text-white">Towerlight Locations</h3>
                    <a href="{% url 'monitoring:manage_infrastructure' %}" class="px-3 py-1 text-sm text-gray-300 hover:text-white bg-accent rounded-lg transition-colors">
                        Manage Infrastructure
                    </a>
                </div>
                <div id="map" class="h-64 rounded-lg"></div>
            </div>
        </div>

        <!-- Recent Alerts -->
        <div class="bg-surface rounded-xl shadow-lg">
            <div class="p-6 border-b border-accent">
                <div class="flex justify-between items-center">
                    <h3 class="text-lg font-semibold text-white">Recent Alerts</h3>
                    <a href="{% url 'monitoring:alerts' %}" class="px-3 py-1 text-sm text-gray-300 hover:text-white bg-accent rounded-lg transition-colors">
                        View All
                    </a>
                </div>
            </div>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-accent">
                    <thead class="bg-surface-dark">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Towerlight ID</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Location</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Time</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Action</th>
                    </tr>
                    </thead>
                    <tbody class="divide-y divide-accent">
                    {% for alert in recent_alerts %}
                        <tr class="hover:bg-accent/30 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ alert.tower.id }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ alert.tower.name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge {% if alert.fault_type == 'power' or alert.fault_type == 'bulb' or alert.fault_type == 'connection' or alert.fault_type == 'other' %}status-red{% elif alert.fault_type == 'maintenance' %}status-orange{% else %}status-gray{% endif %}">
                                    {{ alert.get_fault_type_display }}
                                </span>
                                <span class="status-badge {% if not alert.resolved %}status-green{% else %}status-green{% endif %}">
                                    {% if alert.resolved %}Resolved{% else %}Active{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">{{ alert.timestamp|date:"F j, Y, g:i A" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="{% url 'monitoring:alert_detail' alert.id %}" class="text-primary hover:text-primary-light transition-colors">View Details</a>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script>
        // Initialize Activity Chart with dark theme
        const ctx = document.getElementById('activityChart').getContext('2d');
        Chart.defaults.color = '#94a3b8';
        Chart.defaults.borderColor = 'rgba(255, 255, 255, 0.1)';

        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Active Lights',
                    data: [65, 59, 80, 81, 56, 55, 40],
                    borderColor: '#3D8FCA',
                    backgroundColor: 'rgba(61, 143, 202, 0.1)',
                    tension: 0.4,
                    fill: true
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(255, 255, 255, 0.1)'
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        }
                    }
                }
            }
        });

        // Initialize Map with dark theme
        const map = L.map('map').setView([-20.1560, 28.5885], 13);
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        }).addTo(map);

        // Custom icon creation function
        function createTowerIcon(status) {
            return L.divIcon({
                html: `<img src="/static/icons/tower-${status}.svg" class="tower-icon">`,
                className: 'tower-marker-icon',
                iconSize: [24, 24],
                iconAnchor: [12, 24],
                popupAnchor: [0, -24]
            });
        }

        let markers = {};

        // Fetch tower locations from the API
        fetch('/api/tower-locations/')
            .then(response => response.json())
            .then(data => {
                data.towers.forEach(tower => {
                    const icon = createTowerIcon(tower.status);

                    const marker = L.marker([tower.lat, tower.lng], {
                        icon: icon
                    }).addTo(map);

                    const statusClass = `status-${tower.status}`;

                    const popupContent = `
                        <div class="tower-popup">
                            <h3 class="text-lg font-semibold">${tower.name}</h3>
                            <div class="tower-status ${statusClass}">
                                ${tower.status.charAt(0).toUpperCase() + tower.status.slice(1)}
                            </div>
                            <div class="mt-3">
                                <a href="/manage-infrastructure/${tower.id}"
                                   class="text-primary hover:text-primary-light text-sm">
                                    Manage Tower
                                </a>
                            </div>
                        </div>
                    `;

                    marker.bindPopup(popupContent, {
                        className: 'tower-popup'
                    });

                    markers[tower.id] = marker;
                });
            });

        // Add hover effects to cards
        document.addEventListener('DOMContentLoaded', () => {
            const cards = document.querySelectorAll('.gradient-card');
            cards.forEach(card => {
                card.addEventListener('mouseover', () => {
                    gsap.to(card, { y: -5, boxShadow: '0 8px 20px rgba(0,0,0,0.3)', duration: 0.3 });
                });
                card.addEventListener('mouseout', () => {
                    gsap.to(card, { y: 0, boxShadow: 'none', duration: 0.3 });
                });
            });
        });

        // Add click events to cards for map animations
        document.getElementById('active-lights-card').addEventListener('click', () => {
            Object.values(markers).forEach(marker => {
                if (marker.options.fillColor === '#3D8FCA') {
                    gsap.to(marker._path, {
                        scale: 1.5,
                        opacity: 0.5,
                        duration: 0.5,
                        yoyo: true,
                        repeat: 3,
                        ease: "power2.inOut"
                    });
                }
            });
        });

        document.getElementById('faulty-lights-card').addEventListener('click', () => {
            Object.entries(markers).forEach(([id, marker]) => {
                const icon = marker.getIcon();
                if (icon.options.html.includes('tower-faulty.svg')) {
                    const element = marker.getElement();
                    gsap.to(element, {
                        scale: 1.5,
                        opacity: 0.5,
                        duration: 0.5,
                        yoyo: true,
                        repeat: 3,
                        ease: "power2.inOut"
                    });
                }
            });
        });

        document.getElementById('maintenance-card').addEventListener('click', () => {
            Object.entries(markers).forEach(([id, marker]) => {
                const icon = marker.getIcon();
                if (icon.options.html.includes('tower-maintenance.svg')) {
                    const element = marker.getElement();
                    gsap.to(element, {
                        scale: 1.5,
                        opacity: 0.5,
                        duration: 0.5,
                        yoyo: true,
                        repeat: 3,
                        ease: "power2.inOut"
                    });
                }
            });
        });

        function updateDashboard() {
            // Fetch updated dashboard data
            fetch('/api/dashboard-data/')
                .then(response => response.json())
                .then(data => {
                    // Update status counts
                    document.querySelector('#active-lights-card h3').textContent = data.active_count;
                    document.querySelector('#faulty-lights-card h3').textContent = data.faulty_count;
                    document.querySelector('#maintenance-card h3').textContent = data.maintenance_count;

                    // Update efficiency from sensor data
                    if (data.overall_efficiency) {
                        document.querySelector('#efficiency-card h3').textContent = data.overall_efficiency + '%';
                    }

                    // Update tower markers on map
                    data.towers.forEach(tower => {
                        const marker = markers[tower.id];
                        if (marker) {
                            // Update marker icon based on status
                            const icon = createTowerIcon(tower.status);
                            marker.setIcon(icon);

                            // Update popup with sensor data if available
                            if (data.sensor_data && data.sensor_data[tower.id]) {
                                const sensorData = data.sensor_data[tower.id];
                                const statusClass = `status-${tower.status}`;
                                const popupContent = `
                                    <div class="tower-popup">
                                        <h3 class="text-lg font-semibold">${marker.getPopup().getContent().match(/<h3[^>]*>(.*?)<\/h3>/)[1]}</h3>
                                        <div class="tower-status ${statusClass}">
                                            ${tower.status.charAt(0).toUpperCase() + tower.status.slice(1)}
                                        </div>
                                        <div class="mt-2 text-sm">
                                            <p><strong>Light Level:</strong> ${sensorData.light_level}%</p>
                                            <p><strong>Efficiency:</strong> ${sensorData.efficiency}%</p>
                                            <p><strong>Faulty Lights:</strong> ${sensorData.faulty_lights}</p>
                                            <p><strong>Maintenance Needed:</strong> ${sensorData.maintenance_lights}</p>
                                            <p class="text-xs text-gray-400 mt-1">Last updated: ${sensorData.timestamp}</p>
                                        </div>
                                        <div class="mt-3">
                                            <a href="/manage-infrastructure/${tower.id}"
                                               class="text-primary hover:text-primary-light text-sm">
                                                Manage Tower
                                            </a>
                                        </div>
                                    </div>
                                `;
                                marker.getPopup().setContent(popupContent);
                            }
                        }
                    });

                    // Update recent alerts table
                    const alertsTable = document.querySelector('table tbody');
                    alertsTable.innerHTML = data.recent_alerts.map(alert => `
                        <tr class="hover:bg-accent/30 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${alert.tower_id}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">${alert.tower_name}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge {% if alert.fault_type == 'power' or alert.fault_type == 'bulb' or alert.fault_type == 'connection' or alert.fault_type == 'other' %}status-red{% elif alert.fault_type == 'maintenance' %}status-orange{% else %}status-gray{% endif %}">
                                    {{ alert.get_fault_type_display }}
                                </span>
                                <span class="status-badge {% if not alert.resolved %}status-green{% else %}status-green{% endif %}">
                                    {% if alert.resolved %}Resolved{% else %}Active{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">${alert.timestamp}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="/monitoring/alert/${alert.id}/" class="text-primary hover:text-primary-light transition-colors">View Details</a>
                            </td>
                        </tr>
                    `).join('');
                })
                .catch(error => console.error('Error updating dashboard:', error));
        }

        function getFaultTypeClass(faultType) {
            switch(faultType) {
                case 'power': return 'bg-red-900 text-red-200';
                case 'bulb': return 'bg-yellow-900 text-yellow-200';
                case 'connection': return 'bg-blue-900 text-blue-200';
                default: return 'bg-green-900 text-green-200';
            }
        }

        // Start more frequent periodic updates for real-time feel
        setInterval(updateDashboard, 1000); // Update every second
    </script>


{% endblock %}

