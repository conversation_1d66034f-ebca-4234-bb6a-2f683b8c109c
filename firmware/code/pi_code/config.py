# --- WiFi Credentials ---
WIFI_SSID = "AlensteinM"               # Your WiFi SSID
WIFI_PASSWORD = "_4067m3@/X"         # Your WiFi Password
WIFI_RETRY_DELAY_MS = 5000          # Milliseconds to wait between WiFi connection retries
WIFI_MAX_RETRIES = 20               # Maximum number of WiFi connection retries
WIFI_CONNECT_TIMEOUT_MS = 20000     # Timeout for initial WiFi connection attempt (ms), 0 for no timeout

# --- Server Configuration ---
SERVER_HOST = "*************"     # Your Django server IP/hostname
SERVER_PORT = 8000                  # Your Django server port
USE_HTTPS = False                   # Set to True if using HTTPS for server communication

# API Endpoints (relative to SERVER_HOST:SERVER_PORT)
API_BASE_PATH = "/api"                                # Base path for all API calls
SENSOR_DATA_ENDPOINT_PATH = "/monitoring/sensor-data/"  # Endpoint for sending sensor data
STATUS_UPDATE_ENDPOINT_PATH = "/tower-status-update/" # Endpoint for sending status updates
COMMAND_POLL_ENDPOINT_PATH = "/tower-commands/"       # Endpoint for polling commands

# --- <PERSON>ce Configuration ---
TOWER_ID = 1                        # Unique Tower ID
FIRMWARE_VERSION = "1.0.4-pico"     # Firmware version

# --- Pin Definitions (Raspberry Pi Pico W GP numbers) ---
# Analog Inputs (ADC)
VOLTAGE_PIN_ADC_NUM = 0     # ADC0 is GP26
CURRENT_PIN_ADC_NUM = 1     # ADC1 is GP27
LDR_PIN_ADC_NUM = 2         # ADC2 is GP28

# Digital I/O
RELAY_PIN_NUM = 21          # Example: GP21 for relay control
STATUS_LED_PIN_NUM = "LED"  # Onboard LED on Pico W

# --- ADC General Configuration ---
ADC_MAX_VALUE = 65535.0     # Max value for 16-bit ADC (Pico W)
ADC_VREF = 3.3              # Pico W ADC reference voltage (V)

# --- Voltage Sensor Configuration ---
VOLTAGE_R1 = 30000.0                # R1 for voltage divider
VOLTAGE_R2 = 7500.0                 # R2 for voltage divider
# VOLTAGE_DIVIDER_RATIO is calculated in main code
VOLTAGE_SENSE_MAX_EXPECTED = 12.3   # Max valid voltage reading (before divider)
VOLTAGE_SENSE_NOISE_THRESHOLD = 0.1 # Voltage noise threshold (after calculation)

# --- Current Sensor Configuration ---
# For ACS712 5A version: Sensitivity is 185mV/A.
CURRENT_SCALE_FACTOR = 0.185        # Sensitivity in V/A (e.g., 0.185V/A for 5A module)
CURRENT_NUM_SAMPLES = 100           # Number of samples for current averaging
# Calibrate this value by measuring the sensor's output voltage with no current flowing.
CURRENT_ZERO_POINT_VOLTAGE = 1.65   # Theoretical Vout at 0A if sensor is powered by 3.3V. Adjust this!
CURRENT_MAX_EXPECTED = 1.5          # Upper limit for real current (A)
CURRENT_NOISE_THRESHOLD = 0.05      # Ignore small current fluctuations (A)

# --- LDR & Lux Calibration ---
LDR_DIVIDER_RESISTOR = 10000.0      # Value of the fixed resistor in the LDR voltage divider
# Min ADC value (brightest light, lowest LDR resistance)
LDR_RAW_ADC_MIN = 3000              # Example: Corresponds to ~0.15V for a bright LDR
# Max ADC value (darkest light, highest LDR resistance)
LDR_RAW_ADC_MAX = 60000             # Example: Corresponds to ~3.0V for a dark LDR
# Lux estimation parameters (these are highly empirical and need calibration)
LUX_EST_M = 500000.0                # Placeholder - needs calibration
LUX_EST_C = 0.0                     # Placeholder - needs calibration
MAX_EXPECTED_LUX = 2000.0

# --- Control Thresholds ---
VOLTAGE_LOW_THRESHOLD = 10.0
VOLTAGE_HIGH_THRESHOLD = 12.5
CURRENT_LOW_THRESHOLD = 0.02
CURRENT_HIGH_THRESHOLD = 2.50
POWER_LOW_THRESHOLD = 2.0
LIGHT_PERCENT_DARK_THRESHOLD = 20   # Percentage

# --- Timing Configuration (milliseconds) ---
READ_INTERVAL_MS = 200
DATA_SEND_INTERVAL_MS = 30000
STATUS_UPDATE_INTERVAL_MS = 60000
DISPLAY_UPDATE_INTERVAL_MS = 10000
COMMAND_POLL_INTERVAL_MS = 5000       # Poll for commands every 5 seconds

# --- Error Retry Configuration ---
MAX_HTTP_RETRIES = 3
HTTP_RETRY_DELAY_MS = 2000

# --- Manual Control Timeout ---
MANUAL_CONTROL_TIMEOUT_MS = 300000  # 5 minutes

# --- Time Configuration (NTP and Night Hours) ---
TIMEZONE_OFFSET_SECONDS = 2 * 3600  # Example: GMT+2 in seconds

NIGHT_HOUR_START = 18
NIGHT_MINUTE_START = 0
NIGHT_HOUR_END = 6
NIGHT_MINUTE_END = 0