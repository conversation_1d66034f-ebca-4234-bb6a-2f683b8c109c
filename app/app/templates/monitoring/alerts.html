{% extends 'base.html' %}

{% block title %}Alerts - Tower Light Monitoring{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-white mb-8">Alerts</h1>

        <div class="bg-surface rounded-xl shadow-lg overflow-hidden">
            <div class="p-6 border-b border-accent">
                <form method="get" class="flex flex-wrap gap-4">
                    <div class="flex-1">
                        <label for="status" class="block text-sm font-medium text-gray-300 mb-1">Status</label>
                        <select id="status" name="status" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                            <option value="">All</option>
                            <option value="active">Active</option>
                            <option value="resolved">Resolved</option>
                        </select>
                    </div>
                    <div class="flex-1">
                        <label for="start_date" class="block text-sm font-medium text-gray-300 mb-1">Start Date</label>
                        <input type="date" id="start_date" name="start_date" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                    </div>
                    <div class="flex-1">
                        <label for="end_date" class="block text-sm font-medium text-gray-300 mb-1">End Date</label>
                        <input type="date" id="end_date" name="end_date" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                    </div>
                    <div class="flex-1 flex items-end">
                        <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out">
                            Filter
                        </button>
                    </div>
                </form>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-accent">
                    <thead class="bg-surface-dark">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Tower
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Fault Type
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Status
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Timestamp
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                    </thead>
                    <tbody class="bg-surface divide-y divide-accent">
                    {% for alert in alerts %}
                        <tr class="hover:bg-accent/30 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ alert.tower.name }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                <span class="status-badge {% if alert.fault_type == 'power' or alert.fault_type == 'bulb' or alert.fault_type == 'connection' or alert.fault_type == 'other' %}status-red{% elif alert.fault_type == 'maintenance' %}status-orange{% else %}status-gray{% endif %}">
                                    {{ alert.get_fault_type_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge {% if alert.resolved %}status-green{% else %}status-green{% endif %}">
                                    {% if alert.resolved %}Resolved{% else %}Active{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">{{ alert.timestamp|date:"F j, Y, g:i a" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <a href="{% url 'monitoring:alert_detail' alert.id %}" class="text-primary hover:text-primary-light transition-colors">View Details</a>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-400">
                                No alerts found.
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if is_paginated %}
                <div class="bg-surface px-4 py-3 flex items-center justify-between border-t border-accent sm:px-6">
                    <div class="flex-1 flex justify-between sm:hidden">
                        {% if page_obj.has_previous %}
                            <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-4 py-2 border border-accent text-sm font-medium rounded-md text-gray-400 bg-surface hover:bg-accent">
                                Previous
                            </a>
                        {% endif %}
                        {% if page_obj.has_next %}
                            <a href="?page={{ page_obj.next_page_number }}" class="ml-3 relative inline-flex items-center px-4 py-2 border border-accent text-sm font-medium rounded-md text-gray-400 bg-surface hover:bg-accent">
                                Next
                            </a>
                        {% endif %}
                    </div>
                    <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                        <div>
                            <p class="text-sm text-gray-400">
                                Showing
                                <span class="font-medium text-gray-300">{{ page_obj.start_index }}</span>
                                to
                                <span class="font-medium text-gray-300">{{ page_obj.end_index }}</span>
                                of
                                <span class="font-medium text-gray-300">{{ page_obj.paginator.count }}</span>
                                results
                            </p>
                        </div>
                        <div>
                            <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                                {% if page_obj.has_previous %}
                                    <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-accent bg-surface text-sm font-medium text-gray-400 hover:bg-accent">
                                        <span class="sr-only">Previous</span>
                                        <!-- Heroicon name: solid/chevron-left -->
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}

                                {% for i in paginator.page_range %}
                                    {% if page_obj.number == i %}
                                        <span class="relative inline-flex items-center px-4 py-2 border border-accent bg-accent text-sm font-medium text-gray-300">
                                    {{ i }}
                                </span>
                                    {% else %}
                                        <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-accent bg-surface text-sm font-medium text-gray-400 hover:bg-accent">
                                            {{ i }}
                                        </a>
                                    {% endif %}
                                {% endfor %}

                                {% if page_obj.has_next %}
                                    <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-accent bg-surface text-sm font-medium text-gray-400 hover:bg-accent">
                                        <span class="sr-only">Next</span>
                                        <!-- Heroicon name: solid/chevron-right -->
                                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                                        </svg>
                                    </a>
                                {% endif %}
                            </nav>
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <style>
        .status-badge {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-top: 4px;
        }
        .status-green { background: rgba(34,197,94,0.15); color: #22c55e; }
        .status-red { background: rgba(239,68,68,0.15); color: #ef4444; }
        .status-orange { background: rgba(251,191,36,0.15); color: #fbbf24; }
        .status-gray { background: rgba(156,163,175,0.15); color: #9ca3af; }
    </style>
{% endblock %}

