#include <Arduino.h>

// --- Pin Definitions ---
#define VOLTAGE_PIN 32     // ADC1_CH6

// --- Sensor Calibration & Constants ---
#define VOLTAGE_DIVIDER_RATIO 5.0 // Adjust if your sensor ratio is different
#define ADC_MAX 4095.0
#define ADC_VREF 3.3

// --- Butterworth Filter Configuration ---
// Assuming 5 Hz sampling rate (READ_INTERVAL_MS = 200ms) and 0.2 Hz cutoff
const unsigned long READ_INTERVAL_MS = 200; // How often to read
const double BUTTER_B0 = 0.013368;
const double BUTTER_B1 = 0.026736;
const double BUTTER_B2 = 0.013368;
const double BUTTER_A1 = -1.647198;
const double BUTTER_A2 = 0.700802;

// Filter state variables for Voltage
double volt_x_hist[2] = {0.0, 0.0};
double volt_y_hist[2] = {0.0, 0.0};
bool volt_filterInitialized = false;

// --- Global Variables ---
unsigned long lastReadTime = 0;

// --- Function Prototypes ---
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized);
double readFilteredVoltage();

// ========================== SETUP ==========================
void setup() {
  Serial.begin(115200);
  while (!Serial); // Wait for Serial connection
  Serial.println("\n--- Subsystem Test: DC Voltage Measurement ---");
  Serial.print("Reading from ADC pin: "); Serial.println(VOLTAGE_PIN);
  Serial.print("Voltage Divider Ratio: "); Serial.println(VOLTAGE_DIVIDER_RATIO);
  Serial.print("Read Interval (ms): "); Serial.println(READ_INTERVAL_MS);
  Serial.println("---------------------------------------------");

  pinMode(VOLTAGE_PIN, INPUT);

  // Initialize filter by reading once
  readFilteredVoltage();
  lastReadTime = millis();
}

// ========================== LOOP ==========================
void loop() {
  unsigned long currentTime = millis();

  if (currentTime - lastReadTime >= READ_INTERVAL_MS) {
    lastReadTime = currentTime;

    // Read raw ADC value first for comparison
    int rawAdc = analogRead(VOLTAGE_PIN);
    double rawSensorOutputVoltage = (rawAdc / ADC_MAX) * ADC_VREF;
    double rawActualVoltage = rawSensorOutputVoltage * VOLTAGE_DIVIDER_RATIO;

    // Read the filtered voltage
    double filteredVoltage = readFilteredVoltage();

    // Print results
    Serial.print("Raw ADC: "); Serial.print(rawAdc);
    Serial.print("\tRaw Voltage: "); Serial.print(rawActualVoltage, 2);
    Serial.print("\tFiltered Voltage: "); Serial.print(filteredVoltage, 2);
    Serial.println(" V");
  }
  // No delay needed, timing is handled by millis()
}

// ========================== HELPER FUNCTIONS ==========================

/**
 * @brief Applies a 2nd order Butterworth low-pass filter.
 */
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized) {
  double xn = rawValue;
  if (!initialized) {
      x_hist[0] = xn; x_hist[1] = xn;
      y_hist[0] = xn; y_hist[1] = xn;
      initialized = true;
  }
  double yn = BUTTER_B0 * xn + BUTTER_B1 * x_hist[0] + BUTTER_B2 * x_hist[1] - BUTTER_A1 * y_hist[0] - BUTTER_A2 * y_hist[1];
  x_hist[1] = x_hist[0]; x_hist[0] = xn;
  y_hist[1] = y_hist[0]; y_hist[0] = yn;
  return yn;
}

/**
 * @brief Reads the voltage sensor and applies the Butterworth filter.
 * @return Filtered voltage reading in Volts.
 */
double readFilteredVoltage() {
  int rawAdc = analogRead(VOLTAGE_PIN);
  double sensorOutputVoltage = (rawAdc / ADC_MAX) * ADC_VREF;
  double actualVoltage = sensorOutputVoltage * VOLTAGE_DIVIDER_RATIO;
  // Apply filter to the calculated actual voltage
  double filteredV = applyButterworth(actualVoltage > 0.1 ? actualVoltage : 0.0, volt_x_hist, volt_y_hist, volt_filterInitialized);
  return filteredV;
}