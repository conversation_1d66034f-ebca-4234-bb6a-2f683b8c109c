{% extends 'base.html' %}

{% block title %}<PERSON><PERSON> - Tower Light Monitoring{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8 animate-fade-in">
        <h1 class="text-3xl font-bold text-white mb-8"><PERSON><PERSON></h1>

        <div class="bg-surface rounded-xl p-6 shadow-lg mb-8">
            <h2 class="text-2xl font-semibold text-white mb-4">{{ alert.tower.name }} - {{ alert.get_fault_type_display }}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <p class="text-gray-300"><span class="font-semibold">Status:</span>
                        <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full
                        {% if alert.resolved %}bg-green-900 text-green-200{% else %}bg-red-900 text-red-200{% endif %}">
                        {{ alert.resolved|yesno:"Resolved,Active" }}
                    </span>
                    </p>
                    <p class="text-gray-300"><span class="font-semibold">Timestamp:</span> {{ alert.timestamp|date:"F j, Y, g:i A" }}</p>
                    <p class="text-gray-300"><span class="font-semibold">Description:</span> {{ alert.description }}</p>
                </div>
                <div>
                    <p class="text-gray-300"><span class="font-semibold">Tower Location:</span> {{ alert.tower.latitude }}, {{ alert.tower.longitude }}</p>
                    <p class="text-gray-300"><span class="font-semibold">Installation Date:</span> {{ alert.tower.installation_date|date:"F j, Y" }}</p>
                    {% if alert.resolved %}
                        <p class="text-gray-300"><span class="font-semibold">Resolved By:</span> {{ alert.resolved_by.get_full_name }}</p>
                        <p class="text-gray-300"><span class="font-semibold">Resolved At:</span> {{ alert.resolved_at|date:"F j, Y, g:i A" }}</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <div class="bg-surface rounded-xl p-6 shadow-lg">
            <h3 class="text-xl font-semibold text-white mb-4">Related Faults</h3>
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-accent">
                    <thead class="bg-surface-dark">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Fault Type</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Timestamp</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">Action</th>
                    </tr>
                    </thead>
                    <tbody class="divide-y divide-accent">
                    {% for fault in related_faults %}
                        <tr class="hover:bg-accent/30 transition-colors">
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">{{ fault.get_fault_type_display }}</td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="status-badge {% if fault.resolved %}status-green{% else %}status-green{% endif %}">
                                    {% if fault.resolved %}Resolved{% else %}Active{% endif %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">{{ fault.timestamp|date:"F j, Y, g:i A" }}</td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm">
                                <a href="{% url 'monitoring:alert_detail' fault.id %}" class="text-primary hover:text-primary-light transition-colors">View Details</a>
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <style>
        .status-badge {
            display: inline-block;
            padding: 2px 10px;
            border-radius: 12px;
            font-size: 0.875rem;
            font-weight: 600;
            margin-top: 4px;
        }
        .status-green { background: rgba(34,197,94,0.15); color: #22c55e; }
        .status-red { background: rgba(239,68,68,0.15); color: #ef4444; }
        .status-orange { background: rgba(251,191,36,0.15); color: #fbbf24; }
        .status-gray { background: rgba(156,163,175,0.15); color: #9ca3af; }
    </style>
{% endblock %}

