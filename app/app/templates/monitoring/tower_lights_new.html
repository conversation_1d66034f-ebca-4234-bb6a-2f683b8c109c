{% extends 'base.html' %}
{% load static %}

{% block title %}Tower Lights | Monitoring System{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8">Tower Lights</h1>

    <!-- Loading indicator -->
    <div id="loading-indicator" class="fixed top-4 right-4 bg-blue-500 text-white px-4 py-2 rounded shadow-lg z-50" style="display: none;">
        Loading data...
    </div>

    <!-- Alert container for notifications -->
    <div id="alert-container" class="mb-6 hidden">
        <div class="bg-yellow-900/70 border border-yellow-800 text-yellow-100 px-4 py-3 rounded relative" role="alert">
            <strong class="font-bold">Warning! </strong>
            <span id="alert-message" class="block sm:inline">Some towers are not reporting data.</span>
            <button id="close-alert" type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3">
                <svg class="fill-current h-6 w-6 text-yellow-100" role="button" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><title>Close</title><path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/></svg>
            </button>
        </div>
    </div>

    <!-- Tower cards container -->
    <div id="tower-cards-container" class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-3 gap-6">
        <!-- Cards will be inserted here by JavaScript -->
        <div class="col-span-full text-center py-10">
            <div class="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-500 rounded-full" role="status" aria-label="loading">
                <span class="sr-only">Loading...</span>
            </div>
            <p class="mt-2 text-gray-400">Loading tower data...</p>
        </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-center mt-8">
        <nav class="flex items-center space-x-2" aria-label="Pagination">
            <button id="prev-page" class="px-3 py-2 rounded-md bg-accent/20 text-white hover:bg-accent/40 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                Previous
            </button>
            <span id="pagination-info" class="px-4 py-2 text-white">
                Page <span id="current-page">1</span> of <span id="total-pages">1</span>
            </span>
            <button id="next-page" class="px-3 py-2 rounded-md bg-accent/20 text-white hover:bg-accent/40 transition-colors disabled:opacity-50 disabled:cursor-not-allowed">
                Next
            </button>
        </nav>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Variables
        let currentPage = 1;
        let totalPages = 1;
        let isFirstLoad = true;

        // DOM Elements
        const cardsContainer = document.getElementById('tower-cards-container');
        const prevPageBtn = document.getElementById('prev-page');
        const nextPageBtn = document.getElementById('next-page');
        const currentPageSpan = document.getElementById('current-page');
        const totalPagesSpan = document.getElementById('total-pages');
        const loadingIndicator = document.getElementById('loading-indicator');
        const alertContainer = document.getElementById('alert-container');
        const alertMessage = document.getElementById('alert-message');
        const closeAlertBtn = document.getElementById('close-alert');

        // Close alert when clicking the close button
        closeAlertBtn.addEventListener('click', function() {
            alertContainer.classList.add('hidden');
        });

        // Set up pagination event listeners
        prevPageBtn.addEventListener('click', function() {
            if (currentPage > 1) {
                currentPage--;
                fetchTowerData();
            }
        });

        nextPageBtn.addEventListener('click', function() {
            if (currentPage < totalPages) {
                currentPage++;
                fetchTowerData();
            }
        });

        // Initial data fetch
        fetchTowerData();

        // Set up interval for real-time updates
        setInterval(fetchTowerData, 100); // Update every second

        // Set up page auto-refresh every 5 minutes
        setInterval(function() {
            window.location.reload();
        }, 300000); // 5 minutes

        // Main function to fetch tower data
        function fetchTowerData() {
            // Show loading indicator
            loadingIndicator.style.display = 'block';

            // Fetch data from API
            console.log(`Fetching data from: /api/tower-data/?page=${currentPage}`);

            fetch(`/api/tower-data/?page=${currentPage}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
                .then(response => {
                    console.log('Response status:', response.status);
                    console.log('Response headers:', [...response.headers.entries()]);

                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }

                    return response.text().then(text => {
                        console.log('Raw response:', text);
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('Error parsing JSON:', e);
                            throw new Error('Invalid JSON response');
                        }
                    });
                })
                .then(data => {
                    console.log('Tower data received:', data);

                    // Update pagination info
                    updatePagination(data.pagination);

                    // Update tower cards
                    updateTowerCards(data.towers);

                    // Hide loading indicator
                    loadingIndicator.style.display = 'none';

                    // First load is complete
                    isFirstLoad = false;
                })
                .catch(error => {
                    console.error('Error fetching tower data:', error);

                    // Show error message
                    alertMessage.textContent = 'Error connecting to the server. Will retry automatically.';
                    alertContainer.classList.remove('hidden');

                    // Hide loading indicator
                    loadingIndicator.style.display = 'none';

                    // If this is the first load and we have no cards, show static cards
                    if (isFirstLoad && cardsContainer.querySelectorAll('.tower-card').length === 0) {
                        console.log('Showing static cards as fallback');
                        cardsContainer.innerHTML = '';

                        // Create static cards for towers 1-9
                        for (let i = 1; i <= 9; i++) {
                            const status = i % 3 === 0 ? 'maintenance' : (i % 7 === 0 ? 'faulty' : 'active');
                            const tower = {
                                id: i,
                                name: `Tower ${i}`,
                                status: status,
                                sensor_data: {
                                    voltage: 23.5,
                                    current: 1.8,
                                    power: 42.3,
                                    light_level: 85,
                                    timestamp: new Date().toLocaleString()
                                }
                            };

                            const card = createTowerCard(tower);
                            cardsContainer.appendChild(card);
                            updateTowerCard(card, tower);
                        }

                        // Update pagination
                        currentPageSpan.textContent = '1';
                        totalPagesSpan.textContent = '1';
                        prevPageBtn.disabled = true;
                        nextPageBtn.disabled = true;

                        // First load is complete
                        isFirstLoad = false;
                    }
                });
        }

        // Update pagination information
        function updatePagination(pagination) {
            currentPage = pagination.current_page;
            totalPages = pagination.total_pages;

            currentPageSpan.textContent = currentPage;
            totalPagesSpan.textContent = totalPages;

            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;
        }

        // Update tower cards with new data
        function updateTowerCards(towers) {
            // If this is the first load, clear the container
            if (isFirstLoad) {
                cardsContainer.innerHTML = '';
            }

            // If no towers, show message
            if (towers.length === 0) {
                if (isFirstLoad) {
                    cardsContainer.innerHTML = '<div class="col-span-full text-center py-10"><p class="text-gray-400">No tower lights found.</p></div>';
                }
                return;
            }

            // Process each tower
            towers.forEach(tower => {
                // Check if we already have a card for this tower
                let card = document.getElementById(`tower-card-${tower.id}`);

                // If no card exists and this is the first load, create one
                if (!card && isFirstLoad) {
                    card = createTowerCard(tower);
                    cardsContainer.appendChild(card);
                }

                // If card exists, update it
                if (card) {
                    updateTowerCard(card, tower);
                }
            });
        }

        // Create a new tower card
        function createTowerCard(tower) {
            const card = document.createElement('div');
            card.id = `tower-card-${tower.id}`;
            card.className = 'tower-card rounded-lg overflow-hidden shadow-lg transition-all duration-500 transform hover:scale-105';

            // Set initial card color based on status
            updateCardStatus(card, tower.status);

            // Create card content
            card.innerHTML = `
                <div class="card-header p-4 text-white flex justify-between items-center">
                    <h2 class="text-xl font-bold">${tower.name || `Tower ${tower.id}`}</h2>
                    <span class="status-badge px-2 py-1 rounded-full text-xs font-semibold">${capitalizeFirstLetter(tower.status)}</span>
                </div>
                <div class="card-body p-4 bg-gray-800 text-white">
                    <!-- Status indicator -->
                    <div class="flex justify-center mb-4">
                        <div class="status-indicator w-4 h-4 rounded-full animate-pulse"></div>
                        <span class="ml-2 text-sm font-medium">${capitalizeFirstLetter(tower.status)}</span>
                    </div>

                    <!-- Circular gauges grid -->
                    <div class="grid grid-cols-2 gap-4 mb-6">
                        <!-- Voltage gauge -->
                        <div class="gauge-container">
                            <div class="gauge voltage-gauge">
                                <div class="gauge-center">
                                    <span class="gauge-value voltage-value">0</span>
                                    <span class="gauge-label">V</span>
                                </div>
                            </div>
                            <div class="text-center text-xs text-gray-400 mt-1">Voltage</div>
                        </div>

                        <!-- Current gauge -->
                        <div class="gauge-container">
                            <div class="gauge current-gauge">
                                <div class="gauge-center">
                                    <span class="gauge-value current-value">0</span>
                                    <span class="gauge-label">A</span>
                                </div>
                            </div>
                            <div class="text-center text-xs text-gray-400 mt-1">Current</div>
                        </div>

                        <!-- Power gauge -->
                        <div class="gauge-container">
                            <div class="gauge power-gauge">
                                <div class="gauge-center">
                                    <span class="gauge-value power-value">0</span>
                                    <span class="gauge-label">W</span>
                                </div>
                            </div>
                            <div class="text-center text-xs text-gray-400 mt-1">Power</div>
                        </div>

                        <!-- Light intensity gauge -->
                        <div class="gauge-container">
                            <div class="gauge light-gauge">
                                <div class="gauge-center">
                                    <span class="gauge-value light-value">0</span>
                                    <span class="gauge-label">%</span>
                                </div>
                            </div>
                            <div class="text-center text-xs text-gray-400 mt-1">Light Intensity</div>
                        </div>
                    </div>

                    <div class="mt-4 text-xs text-gray-500 last-updated">Waiting for data...</div>
                </div>
            `;

            return card;
        }

        // Update an existing tower card with new data
        function updateTowerCard(card, tower) {
            // Update status
            if (tower.status) {
                updateCardStatus(card, tower.status);

                const badge = card.querySelector('.status-badge');
                if (badge) {
                    badge.className = 'status-badge ' + getStatusBadgeClass(tower.status);
                    badge.textContent = capitalizeFirstLetter(tower.status);
                }

                const statusIndicator = card.querySelector('.status-indicator');
                if (statusIndicator) {
                    statusIndicator.className = 'status-indicator w-3 h-3 rounded-full ' + getStatusBadgeClass(tower.status);
                }

                const statusText = card.querySelector('.status-text');
                if (statusText) {
                    statusText.textContent = capitalizeFirstLetter(tower.status);
                    statusText.className = 'ml-2 text-sm font-medium status-badge ' + getStatusBadgeClass(tower.status);
                }
            }

            // Update sensor data if available
            if (tower.sensor_data) {
                const data = tower.sensor_data;
                console.log(`Updating tower ${tower.id} with sensor data:`, data);

                // Update voltage
                updateGauge(card, 'voltage', data.voltage || 0, 24);

                // Update current
                updateGauge(card, 'current', data.current || 0, 2);

                // Update power
                updateGauge(card, 'power', data.power || 0, 50);

                // Update light level
                updateGauge(card, 'light', data.light_level || 0, 100);

                // Update timestamp
                const lastUpdated = card.querySelector('.last-updated');
                if (lastUpdated && data.timestamp) {
                    lastUpdated.textContent = `Last updated: ${data.timestamp}`;
                }

                // Remove no-data class if present
                card.classList.remove('no-data');
            } else {
                // Add no-data class if no sensor data
                card.classList.add('no-data');
            }
        }

        // Update a specific gauge in a card
        function updateGauge(card, type, value, maxValue) {
            // Get gauge elements
            const gauge = card.querySelector(`.${type}-gauge`);
            const valueElement = card.querySelector(`.${type}-value`);

            if (!gauge || !valueElement) return;

            // Ensure value is a number
            value = parseFloat(value);
            if (isNaN(value)) value = 0;

            // Calculate percentage (0-100%)
            const percentage = Math.min(Math.max((value / maxValue) * 100, 0), 100);

            // Update gauge appearance
            gauge.style.setProperty('--gauge-value', `${percentage}%`);

            // Set color based on percentage
            if (percentage < 30) {
                gauge.style.setProperty('--gauge-color', 'var(--gauge-low-color)');
            } else if (percentage < 70) {
                gauge.style.setProperty('--gauge-color', 'var(--gauge-medium-color)');
            } else {
                gauge.style.setProperty('--gauge-color', 'var(--gauge-high-color)');
            }

            // Update value text
            valueElement.textContent = value.toFixed(type === 'current' ? 2 : 1);
        }

        // Update card status color
        function updateCardStatus(card, status) {
            // Remove existing status classes
            card.classList.remove('bg-blue-900', 'bg-red-900', 'bg-yellow-900');

            // Add appropriate class based on status
            if (status === 'active') {
                card.classList.add('bg-blue-900');
                card.querySelector('.card-header').className = 'card-header p-4 text-white flex justify-between items-center bg-blue-800';
            } else if (status === 'faulty') {
                card.classList.add('bg-red-900');
                card.querySelector('.card-header').className = 'card-header p-4 text-white flex justify-between items-center bg-red-800';
            } else if (status === 'maintenance') {
                card.classList.add('bg-yellow-900');
                card.querySelector('.card-header').className = 'card-header p-4 text-white flex justify-between items-center bg-yellow-800';
            }
        }

        // Get status color class
        function getStatusColor(status) {
            if (status === 'active') return 'bg-blue-500';
            if (status === 'faulty') return 'bg-red-500';
            if (status === 'maintenance') return 'bg-yellow-500';
            return 'bg-gray-500';
        }

        // Capitalize first letter of a string
        function capitalizeFirstLetter(string) {
            return string.charAt(0).toUpperCase() + string.slice(1);
        }

        // Get status badge class
        function getStatusBadgeClass(status) {
            if (status === 'active') return 'status-green';
            if (status === 'faulty') return 'status-red';
            if (status === 'maintenance') return 'status-orange';
            return 'status-gray';
        }
    });
</script>

<style>
    :root {
        --gauge-low-color: #FF574D;
        --gauge-medium-color: #FFC85C;
        --gauge-high-color: #3D8FCA;
    }

    .tower-card {
        transition: all 0.3s ease;
    }

    /* Circular gauge styles */
    .gauge-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
    }

    .gauge {
        position: relative;
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: #2A2D31;
        box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.5);
        overflow: hidden;
        --gauge-value: 0%;
        --gauge-color: var(--gauge-medium-color);
    }

    .gauge::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: conic-gradient(
            var(--gauge-color) 0%,
            var(--gauge-color) var(--gauge-value),
            transparent var(--gauge-value),
            transparent 100%
        );
        transition: all 0.5s ease-in-out;
    }

    .gauge::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 60px;
        height: 60px;
        background: #2A2D31;
        border-radius: 50%;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
    }

    .gauge-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        width: 100%;
    }

    .gauge-value {
        font-size: 1rem;
        font-weight: bold;
        color: white;
        line-height: 1;
    }

    .gauge-label {
        font-size: 0.7rem;
        color: #aaa;
    }

    /* No data indicator */
    .no-data {
        position: relative;
        opacity: 0.8;
    }

    .no-data::after {
        content: 'NO DATA';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%) rotate(-30deg);
        font-size: 1.5rem;
        font-weight: bold;
        color: rgba(255, 255, 255, 0.3);
        border: 2px solid rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 0.5rem;
        pointer-events: none;
        z-index: 20;
    }

    .status-badge {
        display: inline-block;
        padding: 2px 10px;
        border-radius: 12px;
        font-size: 0.875rem;
        font-weight: 600;
        margin-top: 4px;
    }
    .status-green { background: rgba(34,197,94,0.15); color: #22c55e; }
    .status-red { background: rgba(239,68,68,0.15); color: #ef4444; }
    .status-orange { background: rgba(251,191,36,0.15); color: #fbbf24; }
    .status-gray { background: rgba(156,163,175,0.15); color: #9ca3af; }
</style>
{% endblock %}
