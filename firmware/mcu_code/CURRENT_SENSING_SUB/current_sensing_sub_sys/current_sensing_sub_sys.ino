#include <Wire.h>
#include <LiquidCrystal_I2C.h>

#define POT_PIN 34              // ADC pin for potentiometer (simulating current measurement)
#define RED_LED 2              // Overcurrent / Undercurrent
#define YELLOW_LED 17          // Warning (High or Low)
#define GREEN_LED 16           // Normal current

LiquidCrystal_I2C lcd(0x27, 20, 4);  // 20x4 LCD, will use 16 chars

const float VREF = 3.3;              // ESP32 ADC Reference Voltage
const int ADC_MAX = 4095;            // ESP32 ADC Resolution (12-bit)

const float SMOOTHING_FACTOR = 0.1;  // Smoothing for stable readings
float smoothedCurrent = 0;           // Stores filtered current value in milliamps

void setup() {
  Serial.begin(115200);
  lcd.init();
  lcd.backlight();

  pinMode(RED_LED, OUTPUT);
  pinMode(YELLOW_LED, OUTPUT);
  pinMode(GREEN_LED, OUTPUT);

  lcd.setCursor(0, 0);
  lcd.print("Current Monitor");
}

void loop() {
  // Read potentiometer value (simulate current)
  int potValue = analogRead(POT_PIN); 
  Serial.print("RAW ADC VAL: ");
  Serial.println(potValue);
  float voltage = (potValue * VREF) / ADC_MAX;  // Convert to voltage (0-3.3V)
  
  // Simulate current using the voltage value from the potentiometer (in milliamps)
  // Assuming 0-3.3V is mapped to 0.0mA to 3300mA
  float simulatedCurrent = (voltage / VREF) * 3300;  // Scale the voltage to current in milliamps

  // Apply smoothing filter
  smoothedCurrent = (SMOOTHING_FACTOR * simulatedCurrent) + ((1 - SMOOTHING_FACTOR) * smoothedCurrent);

  // Display current on LCD
  lcd.setCursor(0, 1);
  lcd.print("Current: ");
  lcd.print(smoothedCurrent, 1);  // Display with 1 decimal place
  lcd.print("mA  ");

  lcd.setCursor(0, 2);

  // Check current ranges and control LEDs accordingly
  if (smoothedCurrent < 100) {
    digitalWrite(RED_LED, HIGH);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("LOW CURRENT!  ");
  }
  else if (smoothedCurrent >= 100 && smoothedCurrent < 200) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, HIGH);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("LOW CUR WARN!  ");
  }
  else if (smoothedCurrent >= 200 && smoothedCurrent <= 700) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, HIGH);
    lcd.print("NORMAL CURRENT ");
  }
  else if (smoothedCurrent > 700 && smoothedCurrent <= 900) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, HIGH);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("HIGH CUR WARN! ");
  }
  else {
    digitalWrite(RED_LED, HIGH);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("OVER CURRENT!! ");
  }

  // Print current to serial monitor
  Serial.print("Simulated Current: ");
  Serial.print(smoothedCurrent);
  Serial.println("mA");

  delay(1000);  // Update every 10 milli seconds
}
