"""
<PERSON><PERSON>t to ensure all tower lights 3-20 are set to "active" status in the database.
"""

import os
import sys
import django

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

# Import models after Django setup
from monitoring.models import TowerLight

def ensure_active_towers():
    """Ensure all tower lights 3-20 are set to 'active' status"""
    try:
        print("\nEnsuring Tower Lights 3-20 are Active")
        print("====================================")
        
        # Get all towers
        towers = TowerLight.objects.all().order_by('id')
        
        # Print current status
        print("\nCurrent Status:")
        for tower in towers:
            if 3 <= tower.id <= 20:
                print(f"Tower {tower.id}: {tower.status}")
        
        # Update towers 3-20 to have 'active' status
        updated_count = 0
        for tower in towers:
            if 3 <= tower.id <= 20 and tower.status != 'active':
                old_status = tower.status
                tower.status = 'active'
                tower.save()
                updated_count += 1
                print(f"Updated Tower {tower.id}: {old_status} -> active")
        
        # Print final status
        if updated_count > 0:
            print(f"\nUpdated {updated_count} towers to 'active' status")
            print("\nFinal Status:")
            for tower in TowerLight.objects.filter(id__range=(3, 20)).order_by('id'):
                print(f"Tower {tower.id}: {tower.status}")
        else:
            print("\nAll towers 3-20 are already set to 'active' status")
        
        print("\nDone!")
        
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    ensure_active_towers()
