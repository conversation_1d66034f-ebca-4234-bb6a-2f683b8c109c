# Generated by Django 5.1.4 on 2025-01-08 23:37

import django.core.validators
import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TowerLight',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.Char<PERSON>ield(max_length=100)),
                ('latitude', models.FloatField(validators=[django.core.validators.MinValueValidator(-90), django.core.validators.MaxValueValidator(90)])),
                ('longitude', models.FloatField(validators=[django.core.validators.MinValueValidator(-180), django.core.validators.MaxValueValidator(180)])),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('faulty', 'Faulty'), ('maintenance', 'Under Maintenance')], default='inactive', max_length=20)),
                ('last_checked', models.DateTimeField(auto_now=True)),
                ('installation_date', models.DateField()),
                ('description', models.TextField(blank=True)),
            ],
        ),
        migrations.CreateModel(
            name='Technician',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('phone_number', models.CharField(max_length=15)),
                ('region', models.CharField(max_length=100)),
                ('is_supervisor', models.BooleanField(default=False)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='MaintenanceSchedule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('scheduled_date', models.DateTimeField()),
                ('completed', models.BooleanField(default=False)),
                ('notes', models.TextField(blank=True)),
                ('technician', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='scheduled_maintenance', to='monitoring.technician')),
                ('tower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_schedules', to='monitoring.towerlight')),
            ],
            options={
                'ordering': ['scheduled_date'],
            },
        ),
        migrations.CreateModel(
            name='FaultLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fault_type', models.CharField(choices=[('power', 'Power Failure'), ('bulb', 'Bulb Failure'), ('connection', 'Connection Issue'), ('other', 'Other')], max_length=20)),
                ('description', models.TextField()),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('resolved', models.BooleanField(default=False)),
                ('resolved_at', models.DateTimeField(blank=True, null=True)),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='resolved_faults', to=settings.AUTH_USER_MODEL)),
                ('tower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='faults', to='monitoring.towerlight')),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
    ]
