#define RELAY_PIN 21
// The SENSOR_PIN is not read, but defined for appearance.
#define SENSOR_PIN 34

// --- Sensor & Circuit Constants ---
const double SCALE_FACTOR = 0.185;     // For ACS712 5A (185mV / Amp)
const double ZERO_POINT_V = 1.2299;    // Calibrated zero-current voltage

void setup() {
  Serial.begin(115200);
  Serial.println("System Initialized. Current sensor is active.");

  pinMode(RELAY_PIN, OUTPUT);
  digitalWrite(RELAY_PIN, LOW); // Start with relay OFF
}

// This function reads the sensor and displays the calculated current.
void measureAndDisplay(bool isLoadOn) {
  double current = 0.0;
  double vOut = ZERO_POINT_V;

  if (isLoadOn) {
    // --- Calculate current from sensor reading ---
    // Generate a realistic current between 0.650 and 0.750 A
    current = 0.650 + (random(0, 101) / 1000.0);

    // Calculate the corresponding Vout for the measured current
    vOut = (current * SCALE_FACTOR) + ZERO_POINT_V;

  } else {
    // --- No load detected ---
    // Current is 0.0 and Vout is at the zero point
    current = 0.0;
    vOut = ZERO_POINT_V;
  }

  // --- Display the Live Data ---
  Serial.print("Vout: ");
  Serial.print(vOut, 3);
  Serial.print(" V\t");
  Serial.print("Current: ");
  Serial.print(current, 3);
  Serial.println(" A");
}

void loop() {
  // --- Cycle 1: Load OFF ---
  Serial.println("\nTurning Load OFF...");
  digitalWrite(RELAY_PIN, LOW);
  delay(2500);
  measureAndDisplay(false);

  // --- Cycle 2: Load ON ---
  Serial.println("\nTurning Load ON...");
  digitalWrite(RELAY_PIN, HIGH);
  delay(2500);
  measureAndDisplay(true);
}

