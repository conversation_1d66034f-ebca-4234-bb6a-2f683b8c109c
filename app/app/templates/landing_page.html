{% load static %}
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tower Light Monitoring</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js"></script>
    <style>
        #tower-animation {
            display: flex;
            justify-content: center;
            align-items: center;
            animation: fadeIn 1s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: scale(0.9);
            }
            to {
                opacity: 1;
                transform: scale(1);
            }
        }


        .wave-bg {
            position: relative;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 2rem;
            overflow: hidden;
        }

        .wave-bg::before {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 100px;
            background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 1440 320'%3E%3Cpath fill='%23fff' fill-opacity='0.1' d='M0,96L48,112C96,128,192,160,288,160C384,160,480,128,576,122.7C672,117,768,139,864,154.7C960,171,1056,181,1152,170.7C1248,160,1344,128,1392,112L1440,96L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z'%3E%3C/path%3E%3C/svg%3E") no-repeat bottom;
            background-size: cover;
        }

        @keyframes float {
            0% { transform: translateY(0px); }
            50% { transform: translateY(-20px); }
            100% { transform: translateY(0px); }
        }

        .float {
            animation: float 6s ease-in-out infinite;
        }
    </style>
</head>
<body class="min-h-screen bg-gradient-to-b from-[#F4F5F7] to-[#EAEFF4]">
<!-- Hero Section -->
<section class="relative h-screen flex items-center justify-center overflow-hidden">
    <div class="absolute inset-0 z-0 bg-cover bg-center" style="background-image: url('https://assets.codepen.io/721952/sky.jpg')"></div>
    <div class="container mx-auto px-6 relative z-10">
        <div class="flex flex-col md:flex-row items-center">
            <div class="w-full md:w-1/2 text-center md:text-left" id="hero-content">
                <h1 class="text-5xl md:text-6xl font-bold text-white mb-6">
                    Tower Light Monitoring and Fault Detection System
                </h1>
                <p class="text-xl md:text-2xl text-gray-200 mb-8">
                    Revolutionizing the way we manage and monitor tower lights for enhanced safety and efficiency.
                </p>
                <a href="#cta"
                   class="hover:bg-[#FF574D]/90 text-white font-bold py-3 px-6 rounded-full text-lg inline-block border-2 border-white">
                    Get Started
                </a>

            </div>
            <div class="w-full md:w-1/2 mt-12 md:mt-0">
                <!-- SVG Illustration -->
                <div id="tower-animation" class="w-full max-w-md mx-auto">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 400 400" class="w-full">
                        <!-- Tower -->
                        <rect x="180" y="150" width="40" height="150" rx="8" fill="#23272A"></rect>
                        <line x1="200" y1="150" x2="180" y2="100" stroke="#23272A" stroke-width="6"></line>
                        <line x1="200" y1="150" x2="220" y2="100" stroke="#23272A" stroke-width="6"></line>
                        <circle cx="200" cy="95" r="12" fill="#FF574D"></circle>

                        <!-- Tower Beams -->
                        <circle id="beam1" cx="200" cy="95" r="20" fill="#FFBABA" opacity="0"></circle>
                        <circle id="beam2" cx="200" cy="95" r="40" fill="#FFE4B3" opacity="0"></circle>
                        <circle id="beam3" cx="200" cy="95" r="60" fill="#FFF3E0" opacity="0"></circle>

                        <!-- Streetlights -->
                        <rect x="100" y="200" width="10" height="50" fill="#444"></rect>
                        <circle cx="105" cy="195" r="5" fill="#FF574D"></circle>
                        <rect x="290" y="200" width="10" height="50" fill="#444"></rect>
                        <circle cx="295" cy="195" r="5" fill="#FF574D"></circle>

                        <!-- Streetlight Beams -->
                        <circle id="light1" cx="105" cy="200" r="20" fill="#FFD9A7" opacity="0"></circle>
                        <circle id="light2" cx="295" cy="200" r="20" fill="#FFD9A7" opacity="0"></circle>
                    </svg>
                </div>
            </div>


        </div>
    </div>
</section>

<!-- Features Section -->
<!-- Features Section -->
<section class="py-20 bg-white" id="features">
    <div class="container mx-auto px-6">
        <h2 class="text-4xl font-bold text-center text-[#23272A] mb-12">Key Features</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-12">
            <!-- Real-time Monitoring -->
            <div class="bg-[#EAEFF4] rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mb-6" viewBox="0 0 64 64" fill="none">
                    <!-- Outer Circle -->
                    <circle cx="32" cy="32" r="30" stroke="#5BC0EB" stroke-width="2" fill="none" />

                    <!-- Central IoT Hub -->
                    <circle cx="32" cy="32" r="6" fill="#5BC0EB" />
                    <circle cx="32" cy="32" r="3" fill="#FFFFFF" />

                    <!-- Signal Waves -->
                    <path d="M32 15c7 0 13 6 13 13" stroke="#5BC0EB" stroke-width="2" stroke-linecap="round" />
                    <path d="M32 10c10 0 18 8 18 18" stroke="#5BC0EB" stroke-width="1.5" stroke-linecap="round" />
                    <path d="M32 5c13 0 23 10 23 23" stroke="#5BC0EB" stroke-width="1" stroke-linecap="round" />

                    <!-- Connected Nodes -->
                    <circle cx="20" cy="20" r="3" fill="#6A4C93" />
                    <circle cx="44" cy="20" r="3" fill="#9A65F4" />
                    <circle cx="20" cy="44" r="3" fill="#B280F7" />
                    <circle cx="44" cy="44" r="3" fill="#6A4C93" />

                    <!-- Connecting Lines -->
                    <line x1="32" y1="32" x2="20" y2="20" stroke="#EAEFF4" stroke-width="1.5" />
                    <line x1="32" y1="32" x2="44" y2="20" stroke="#EAEFF4" stroke-width="1.5" />
                    <line x1="32" y1="32" x2="20" y2="44" stroke="#EAEFF4" stroke-width="1.5" />
                    <line x1="32" y1="32" x2="44" y2="44" stroke="#EAEFF4" stroke-width="1.5" />
                </svg>
                <h3 class="text-2xl font-semibold text-[#23272A] mb-4">Real-time Monitoring</h3>
                <p class="text-gray-600">Keep track of all your tower lights in real-time, ensuring optimal performance and quick issue detection.</p>
            </div>

            <!-- Instant Alerts -->
            <div class="bg-[#EAEFF4] rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mb-6" viewBox="0 0 24 24" fill="none">
                    <circle cx="12" cy="12" r="10" fill="#FF6B6B" />
                    <path d="M12 7v5m0 4h.01" stroke="#FFFFFF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    <polygon points="12,2 14,6 10,6" fill="#FFCC29" />
                </svg>
                <h3 class="text-2xl font-semibold text-[#23272A] mb-4">Instant Alerts</h3>
                <p class="text-gray-600">Receive immediate notifications for any issues or anomalies, allowing for swift response and maintenance.</p>
            </div>

            <!-- Comprehensive Reporting -->
            <div class="bg-[#EAEFF4] rounded-lg p-8 shadow-lg hover:shadow-xl transition-shadow duration-300 transform hover:-translate-y-1">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-16 h-16 mb-6" viewBox="0 0 24 24" fill="none">
                    <rect x="4" y="10" width="4" height="8" fill="#6A4C93" rx="1" />
                    <rect x="10" y="6" width="4" height="12" fill="#9A65F4" rx="1" />
                    <rect x="16" y="4" width="4" height="14" fill="#B280F7" rx="1" />
                </svg>
                <h3 class="text-2xl font-semibold text-[#23272A] mb-4">Comprehensive Reporting</h3>
                <p class="text-gray-600">Generate detailed reports on tower light performance, maintenance history, and efficiency metrics.</p>
            </div>
        </div>
    </div>
</section>


<!-- CTA Section -->
<section class="py-20 bg-[#3D8FCA]" id="cta">
    <div class="container mx-auto px-6">
        <div class="wave-bg text-center">
            <h2 class="text-4xl font-bold text-white mb-8">Ready to Optimize Your Tower Light Management?</h2>
            <p class="text-xl text-white mb-12">Join the growing number of companies trusting our system for efficient and reliable tower light monitoring.</p>
            <a href="{% url 'login' %}" class="bg-white text-[#3D8FCA] hover:bg-gray-100 font-bold py-3 px-8 rounded-full text-lg transition duration-300 ease-in-out transform hover:scale-105 inline-block">
                Log In Now
            </a>
        </div>
    </div>
</section>

<!-- Footer -->
<footer class="bg-[#23272A] text-white py-12">
    <div class="container mx-auto px-6">
        <div class="flex flex-wrap justify-between">
            <div class="w-full md:w-1/4 mb-8 md:mb-0">
                <h3 class="text-2xl font-bold mb-4">Tower Light Monitoring</h3>
                <p class="text-gray-400">Revolutionizing tower light management for enhanced safety and efficiency.</p>
            </div>
            <div class="w-full md:w-1/4 mb-8 md:mb-0">
                <h4 class="text-xl font-semibold mb-4">Quick Links</h4>
                <ul class="space-y-2">
                    <li><a href="#" class="hover:text-[#FFC85C] transition-colors duration-300">Home</a></li>
                    <li><a href="#features" class="hover:text-[#FFC85C] transition-colors duration-300">Features</a></li>
                    <li><a href="#hero-content" class="hover:text-[#FFC85C] transition-colors duration-300">Contact</a></li>
                </ul>
            </div>
            <div class="w-full md:w-1/4 mb-8 md:mb-0">
                <h4 class="text-xl font-semibold mb-4">Contact Us</h4>
                <p class="text-gray-400">Email: <EMAIL></p>
                <p class="text-gray-400">Phone: +263 77 851 4979</p>
            </div>
            <div class="w-full md:w-1/4">
                <h4 class="text-xl font-semibold mb-4">Follow Us</h4>
                <div class="flex space-x-4">
                    <!-- Social Icons -->
                    <a href="#" class="text-white hover:text-[#FFC85C] transition-colors duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>
                    </a>
                    <a href="#" class="text-white hover:text-[#FFC85C] transition-colors duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M23.954 4.569c-.885.389-1.83.654-2.825.775 1.014-.611 1.794-1.574 2.163-2.723-.951.555-2.005.959-3.127 1.184-.896-.959-2.173-1.559-3.591-1.559-2.717 0-4.92 2.203-4.92 4.917 0 .39.045.765.127 1.124C7.691 8.094 4.066 6.13 1.64 3.161c-.427.722-.666 1.561-.666 2.475 0 1.71.87 3.213 2.188 4.096-.807-.026-1.566-.248-2.228-.616v.061c0 2.385 1.693 4.374 3.946 4.827-.413.111-.849.171-1.296.171-.314 0-.615-.03-.916-.086.631 1.953 2.445 3.377 4.604 3.417-1.68 1.319-3.809 2.105-6.102 2.105-.39 0-.779-.023-1.17-.067 2.189 1.394 4.768 2.209 7.557 2.209 9.054 0 13.999-7.496 13.999-13.986 0-.209 0-.42-.015-.63.961-.689 1.8-1.56 2.46-2.548l-.047-.02z"/></svg>
                    </a>
                    <a href="#" class="text-white hover:text-[#FFC85C] transition-colors duration-300">
                        <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24"><path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/></svg>
                    </a>
                </div>
            </div>
        </div>
        <div class="mt-12 pt-8 border-t border-gray-700 text-center">
            <p class="text-gray-400">&copy; 2025 Khayelihle Honors Project. All rights reserved.</p>
        </div>
    </div>
</footer>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>

<script src="https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js"></script>
<script>
    // Create an infinite loop animation using GSAP
    gsap.timeline({ repeat: -1 }) // repeat: -1 makes it loop indefinitely
        // Tower lights
        .to("#beam1", { opacity: 1, r: 30, duration: 1.5, ease: "power2.out" })
        .to("#beam2", { opacity: 1, r: 50, duration: 1.5, ease: "power2.out" }, "-=1")
        .to("#beam3", { opacity: 1, r: 70, duration: 1.5, ease: "power2.out" }, "-=1")
        .to(["#beam1", "#beam2", "#beam3"], { opacity: 0, duration: 0.8, ease: "power2.in" }, "-=0.5")

        // Streetlight 1
        .to("#light1", { opacity: 1, r: 30, duration: 1.5, ease: "power2.out" }, "-=1.5")
        .to("#light1", { opacity: 0, duration: 1, ease: "power2.in" }, "-=0.5")

        // Streetlight 2
        .to("#light2", { opacity: 1, r: 30, duration: 1.5, ease: "power2.out" }, "-=1.8")
        .to("#light2", { opacity: 0, duration: 1, ease: "power2.in" }, "-=0.5");
</script>


<script>
    // Initialize GSAP
    gsap.registerPlugin(ScrollTrigger);

    // Hero Animations
    gsap.from("#hero-content h1", {
        opacity: 0,
        y: 50,
        duration: 1,
        delay: 0.2
    });

    gsap.from("#hero-content p", {
        opacity: 0,
        y: 30,
        duration: 1,
        delay: 0.4
    });

    gsap.from("#hero-content a", {
        opacity: 0,
        y: 30,
        duration: 1,
        delay: 0.6
    });

    // Features Animation
    gsap.from("#features .grid > div", {
        scrollTrigger: {
            trigger: "#features",
            start: "top center",
            toggleActions: "play none none reverse"
        },
        opacity: 0,
        y: 50,
        duration: 0.8,
        stagger: 0.2
    });

    // CTA Animation
    gsap.from("#cta .wave-bg", {
        scrollTrigger: {
            trigger: "#cta",
            start: "top center",
            toggleActions: "play none none reverse"
        },
        opacity: 0,
        y: 50,
        duration: 1
    });
</script>
</body>
</html>