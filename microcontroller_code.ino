#include <Arduino.h>
#include <WiFi.h>
#include <WiFiMulti.h>
#include <HTTPClient.h>
#include <ArduinoJson.h>

// Function prototypes (forward declarations)
void connectToWiFi();
void sendSensorData();
void updateTowerStatus(String status, String faultType = "");
void checkAndUpdateStatus();

// WiFi Configuration
WiFiMulti WiFiMulti;
const char* SSID = "Alenstein";      // WiFi SSID
const char* PASSWORD = "$11111111$$$";      // WiFi Password

// API Configuration
const char* BASE_API_URL = "http://192.168.96.190:8000";  // Updated to your port 8880
const char* TOWER_STATUS_ENDPOINT = "/api/tower-status-update/";  // Matches your TowerStatusUpdateView
const char* SENSOR_DATA_ENDPOINT = "/api/monitoring/sensor-data/";  // Your sensor data endpoint

// Tower Configuration
const int TOWER_ID = 1;  // Change to match your tower ID in the database
unsigned long lastStatusUpdate = 0;
unsigned long lastSensorUpdate = 0;
const unsigned long STATUS_UPDATE_INTERVAL = 400;  // 10 seconds
const unsigned long SENSOR_UPDATE_INTERVAL = 500;   // 5 seconds

// Pin Configuration
const int PIN_RED_LIGHT = 12;    // Red light pin (Faulty)
const int PIN_YELLOW_LIGHT = 14; // Yellow light pin (Maintenance)
const int PIN_GREEN_LIGHT = 27;  // Green light pin (Active)
const int PIN_POWER_SENSOR = 34; // Analog input for power monitoring
const int PIN_CONNECTIVITY_LED = 2; // Built-in LED to show API connection status

// Status variables
String currentStatus = "inactive";
bool isConnected = false;

void setup() {
    Serial.begin(115200);
    delay(1000);
    Serial.println("ESP32 Tower Light Controller starting...");
    
    // Initialize pins
    pinMode(PIN_RED_LIGHT, OUTPUT);
    pinMode(PIN_YELLOW_LIGHT, OUTPUT);
    pinMode(PIN_GREEN_LIGHT, OUTPUT);
    pinMode(PIN_CONNECTIVITY_LED, OUTPUT);
    
    // Turn all lights off initially
    digitalWrite(PIN_RED_LIGHT, LOW);
    digitalWrite(PIN_YELLOW_LIGHT, LOW);
    digitalWrite(PIN_GREEN_LIGHT, LOW);
    digitalWrite(PIN_CONNECTIVITY_LED, LOW);

    // Connect to WiFi
    connectToWiFi();
    
    // Send initial status update
    updateTowerStatus("inactive");
}

void loop() {
    // Check if WiFi is connected, reconnect if needed
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi connection lost. Reconnecting...");
        connectToWiFi();
    }
    
    // Send sensor data periodically
    if (millis() - lastSensorUpdate > SENSOR_UPDATE_INTERVAL) {
        sendSensorData();
        lastSensorUpdate = millis();
    }
    
    // Check tower sensors and update status if needed
    checkAndUpdateStatus();
    
    // Small delay to prevent CPU hogging
    delay(100);
}

void connectToWiFi() {
    Serial.print("Connecting to WiFi");
    digitalWrite(PIN_CONNECTIVITY_LED, LOW);
    
    WiFiMulti.addAP(SSID, PASSWORD);
    
    // Wait for connection with timeout
    int timeout = 0;
    while (WiFiMulti.run() != WL_CONNECTED && timeout < 20) {
        delay(500);
        Serial.print(".");
        timeout++;
    }
    
    if (WiFi.status() == WL_CONNECTED) {
        Serial.println();
        Serial.print("Connected to WiFi. IP address: ");
        Serial.println(WiFi.localIP());
        digitalWrite(PIN_CONNECTIVITY_LED, HIGH);
        isConnected = true;
    } else {
        Serial.println();
        Serial.println("Failed to connect to WiFi. Will retry in the main loop.");
        isConnected = false;
    }
}

void sendSensorData() {
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi not connected. Cannot send sensor data.");
        return;
    }
    
    HTTPClient http;
    String url = String(BASE_API_URL) + SENSOR_DATA_ENDPOINT;
    http.begin(url);
    http.addHeader("Content-Type", "application/json");
    
    // Read sensor data
    int sensorValue = analogRead(PIN_POWER_SENSOR);
    int lightLevel = map(sensorValue, 0, 4095, 0, 100);  // ESP32 has 12-bit ADC (0-4095)
    
    // Generate random values for demonstration
    // In a real implementation, these would come from actual sensors
    int faultyLights = random(0, 5);
    int maintenanceLights = random(0, 5);
    int efficiency = random(70, 100);
    
    // Create JSON object
    StaticJsonDocument<256> jsonDoc;
    jsonDoc["tower_id"] = TOWER_ID;
    jsonDoc["sensor_value"] = sensorValue;
    jsonDoc["light_level"] = lightLevel;
    jsonDoc["faulty_lights"] = faultyLights;
    jsonDoc["maintenance_lights"] = maintenanceLights;
    jsonDoc["efficiency"] = efficiency;
    
    String jsonData;
    serializeJson(jsonDoc, jsonData);
    
    // Send data
    int httpResponseCode = http.POST(jsonData);
    
    if (httpResponseCode > 0) {
        Serial.printf("Sensor data sent. HTTP Response: %d\n", httpResponseCode);
        String response = http.getString();
        Serial.println("Response: " + response);
    } else {
        Serial.printf("HTTP Error: %s\n", http.errorToString(httpResponseCode).c_str());
        digitalWrite(PIN_CONNECTIVITY_LED, LOW);
        isConnected = false;
    }
    
    http.end();
}

void updateTowerStatus(String status, String faultType) {
    if (WiFi.status() != WL_CONNECTED) {
        Serial.println("WiFi not connected. Cannot update tower status.");
        return;
    }
    
    // Update lights based on status
    if (status == "active") {
        digitalWrite(PIN_RED_LIGHT, LOW);
        digitalWrite(PIN_YELLOW_LIGHT, LOW);
        digitalWrite(PIN_GREEN_LIGHT, HIGH);
    } else if (status == "inactive") {
        digitalWrite(PIN_RED_LIGHT, LOW);
        digitalWrite(PIN_YELLOW_LIGHT, LOW);
        digitalWrite(PIN_GREEN_LIGHT, LOW);
    } else if (status == "maintenance") {
        digitalWrite(PIN_RED_LIGHT, LOW);
        digitalWrite(PIN_YELLOW_LIGHT, HIGH);
        digitalWrite(PIN_GREEN_LIGHT, LOW);
    } else if (status == "faulty") {
        digitalWrite(PIN_RED_LIGHT, HIGH);
        digitalWrite(PIN_YELLOW_LIGHT, LOW);
        digitalWrite(PIN_GREEN_LIGHT, LOW);
    }
    
    // Update current status
    currentStatus = status;
    
    // Send status update to server
    HTTPClient http;
    String url = String(BASE_API_URL) + TOWER_STATUS_ENDPOINT;
    http.begin(url);
    http.addHeader("Content-Type", "application/json");
    
    // Create JSON object according to your API structure
    StaticJsonDocument<256> jsonDoc;
    jsonDoc["tower_id"] = TOWER_ID;
    jsonDoc["status"] = status;
    
    // Add fault_type if provided and status is faulty
    if (status == "faulty" && faultType.length() > 0) {
        jsonDoc["fault_type"] = faultType;
    }
    
    String jsonData;
    serializeJson(jsonDoc, jsonData);
    
    // Send data
    int httpResponseCode = http.POST(jsonData);
    
    if (httpResponseCode > 0) {
        Serial.printf("Tower status updated to %s. HTTP Response: %d\n", status.c_str(), httpResponseCode);
        String response = http.getString();
        Serial.println("Response: " + response);
    } else {
        Serial.printf("HTTP Error updating tower status: %s\n", http.errorToString(httpResponseCode).c_str());
    }
    
    http.end();
    
    // Update timestamp
    lastStatusUpdate = millis();
}

void checkAndUpdateStatus() {
    // Read power sensor value
    int powerValue = analogRead(PIN_POWER_SENSOR);
    int powerLevel = map(powerValue, 0, 4095, 0, 100);  // ESP32 has 12-bit ADC (0-4095)
    
    // Check if power is too low (potential fault)
    if (currentStatus == "active" && powerLevel < 20) {  // Threshold at 20%
        // Report power fault
        Serial.printf("Power level too low (%d%%). Reporting fault.\n", powerLevel);
        updateTowerStatus("faulty", "power");
    }
    
    // Simulate random faults for demonstration purposes
    // In a real implementation, this would be based on actual sensor readings
    if (currentStatus == "active" && random(1, 1000) == 1) {  // 0.1% chance of random fault
        String faultTypes[] = {"bulb", "connection", "power"};
        String randomFault = faultTypes[random(0, 3)];
        Serial.printf("Random %s fault detected. Reporting fault.\n", randomFault.c_str());
        updateTowerStatus("faulty", randomFault);
    }
}
