from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views

router = DefaultRouter()
router.register(r'towers', views.TowerLightViewSet, basename='tower')
router.register(r'faults', views.FaultLogViewSet)
router.register(r'technicians', views.TechnicianViewSet)
router.register(r'maintenance', views.MaintenanceScheduleViewSet)

app_name = 'api'

urlpatterns = [
    path('', include(router.urls)),
    path('dashboard-data/', views.DashboardDataView.as_view(), name='dashboard-data'),
    path('tower-status-update/', views.TowerStatusUpdateView.as_view(), name='tower-status-update'),
    path('monitoring/sensor-data/', views.SensorDataView.as_view(), name='sensor-data'),
    path('tower-data/', views.TowerDataView.as_view(), name='tower-data'),
    path('active-alerts/', views.ActiveAlertsView.as_view(), name='active-alerts'),
    path('tower-status-update/<int:tower_id>/', views.TowerStatusUpdateView.as_view(), name='tower-status-update-detail'),
    path('tower-commands/<int:tower_id>/', views.TowerCommandView.as_view(), name='tower-commands'),
]

