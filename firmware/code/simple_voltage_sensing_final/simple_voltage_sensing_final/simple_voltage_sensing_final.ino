#define ANALOG_IN_PIN 22
#define RELAY_PIN 21

// Constants
const float R1 = 30000.0;     // 30kΩ resistor
const float R2 = 7500.0;      // 7.5kΩ resistor
const float REF_VOLTAGE = 3.3; // ESP32 reference voltage
const int ADC_RESOLUTION = 4095; // 12-bit ADC for ESP32

const float MAX_VOLTAGE = 12.3;   // Maximum expected voltage
const float NOISE_THRESHOLD = 0.1; // Threshold to ignore noise

void setup() {
  Serial.begin(115200);
  Serial.println("Voltage Sensor and Relay Initialized...");

  // Configure the analog input pin
  pinMode(ANALOG_IN_PIN, INPUT);

  // Configure the relay pin as an output
  pinMode(RELAY_PIN, OUTPUT);

  // Set the ADC attenuation for the analog pin to allow a full range of voltage reading
  analogSetPinAttenuation(ANALOG_IN_PIN, ADC_11db);
}

void readVoltageSensor() {
  int adc_value = analogRead(ANALOG_IN_PIN);

  // Calculate the voltage at the ADC pin
  float adc_voltage = (adc_value * REF_VOLTAGE) / ADC_RESOLUTION;

  // Calculate the input voltage using the voltage divider formula
  float in_voltage = adc_voltage * ((R1 + R2) / R2);

  Serial.print("in_voltage: ");
  Serial.println(in_voltage);
  // Filter out noise and invalid readings
  if (in_voltage < NOISE_THRESHOLD || in_voltage > MAX_VOLTAGE) {
    in_voltage = 0.0;
  }

  // Print the results to the Serial Monitor
  Serial.print("ADC Raw: ");
  Serial.print(adc_value);
  Serial.print("\tInput Voltage: ");
  Serial.print(in_voltage, 2);
  Serial.println(" V");
}

void loop() {
  Serial.println("Turning RELAY ON...");
  digitalWrite(RELAY_PIN, LOW); // Assuming LOW turns the relay off
  readVoltageSensor();
  delay(3000);
}