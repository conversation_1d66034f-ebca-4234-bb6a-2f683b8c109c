# Hardware Setup Guide

## Components List

### Required Components
1. ESP32 Development Board
2. ACS712 Current Sensor (5A version)
3. Voltage Divider (2x resistors: 30kΩ and 7.5kΩ)
4. LDR (Light Dependent Resistor)
5. 5V Relay Module
6. Tower Light (12V DC)
7. Power Supply (12V DC)
8. Status LED (built-in LED on ESP32 or external)

### Optional Components
1. Enclosure for weather protection
2. Terminal blocks for connections
3. Heat shrink tubing for insulation
4. Fuse holder and fuse (1A recommended)

## Wiring Diagram

### Power Supply
```
12V DC Power Supply
├── +12V → Tower Light (+) via Relay NO contact
├── +12V → Voltage Divider Input
└── GND  → Common Ground

ESP32 Power
├── USB Power (for development) or
└── External 5V Supply (for deployment)
```

### Sensor Connections

#### Voltage Sensor (Voltage Divider)
```
12V Input → 30kΩ (R1) → GPIO22 → 7.5kΩ (R2) → GND
```

#### Current Sensor (ACS712)
```
ACS712
├── VCC → 5V
├── GND → GND
├── OUT → GPIO34
└── Current Path: Connect in series with tower light positive wire
```

#### Light Sensor (LDR)
```
LDR Circuit
├── 3.3V → LDR → GPIO33
└── GPIO33 → 10kΩ → GND
```

### Control Connections

#### Relay Module
```
Relay
├── VCC → 5V
├── GND → GND
├── IN  → GPIO21
└── Contacts: COM → 12V, NO → Tower Light
```

#### Status LED
```
Status LED
├── GPIO2 → LED → Resistor (330Ω) → GND
```

## Safety Considerations

1. Power Supply
   - Use proper isolation between high and low voltage circuits
   - Add 1A fuse protection on the 12V input line
   - Ensure proper grounding of all components
   - Use a regulated power supply for stable voltage

2. Voltage Sensing
   - Use proper rated resistors (1/4W minimum)
   - Add protection diodes to prevent overvoltage on GPIO pins
   - Ensure proper isolation between high and low voltage circuits
   - Use heat-resistant wire for connections

3. Current Sensing
   - Use ACS712 5A version as specified in config.h
   - Use proper wire gauge (18 AWG recommended for tower light)
   - Consider heat dissipation for the current sensor
   - Mount sensor away from heat-sensitive components

4. General Safety
   - Use proper insulation for all connections
   - Add strain relief for all external cables
   - Use a weather-resistant enclosure for outdoor installations
   - Include ventilation if installed in enclosed spaces

## Calibration Steps

1. Voltage Sensor
   ```
   1. Apply known voltage (use multimeter to measure actual voltage)
   2. Read ADC value via Serial Monitor
   3. Adjust VOLTAGE_R1 and VOLTAGE_R2 in config.h to match your actual resistor values
   4. Verify linear response across expected voltage range (0-15V)
   5. Check VOLTAGE_SENSE_MAX_EXPECTED and VOLTAGE_SENSE_NOISE_THRESHOLD settings
   ```

2. Current Sensor
   ```
   1. No-load zero calibration: Measure output voltage with no current flowing
   2. Update CURRENT_ZERO_POINT in config.h with the measured value
   3. Apply known load and measure actual current with multimeter
   4. Adjust CURRENT_SCALE_FACTOR if needed (0.185 for 5A ACS712)
   5. Verify CURRENT_NOISE_THRESHOLD is appropriate for your setup
   ```

3. LDR Calibration
   ```
   1. Cover LDR completely (dark condition) and record ADC value
   2. Update LDR_RAW_ADC_MIN in config.h
   3. Expose LDR to bright light and record ADC value
   4. Update LDR_RAW_ADC_MAX in config.h
   5. Verify light level percentage calculation in different lighting conditions
   ```

## Network Configuration

1. WiFi Setup
   ```
   1. Update WIFI_SSID and WIFI_PASSWORD in config.h
   2. Set appropriate WIFI_RETRY_DELAY and WIFI_MAX_RETRIES
   3. Adjust WIFI_CONNECT_TIMEOUT_MS based on your network conditions
   ```

2. Server Configuration
   ```
   1. Update SERVER_HOST with your server's IP address or hostname
   2. Set SERVER_PORT to match your Django server (default: 8000)
   3. Set USE_HTTPS to true if your server uses HTTPS
   4. Verify API_BASE_PATH and endpoint paths match your server configuration
   ```

3. Tower Identification
   ```
   1. Set unique TOWER_ID for each microcontroller unit
   2. Update FIRMWARE_VERSION when making code changes
   ```

## Testing Procedure

1. Power-up Test
   - Check voltage levels at all test points
   - Verify ESP32 boots successfully (monitor Serial output)
   - Confirm status LED blinks during initialization
   - Check all sensor readings are within expected ranges

2. Sensor Verification
   - Monitor serial output for sensor readings
   - Verify voltage readings match multimeter measurements
   - Check current readings with known loads
   - Verify light sensor responds to changing light conditions

3. Control Testing
   - Test relay operation (should hear click and see LED indicator)
   - Verify relay turns on/off based on time of day and light level
   - Check fault detection for various conditions
   - Test automatic recovery from fault conditions

4. Network Testing
   - Verify WiFi connection is stable
   - Test API communication with server
   - Confirm data is being received by the monitoring system
   - Check status updates are reflected in the web interface

## Troubleshooting

### Common Hardware Issues

1. Erratic Readings
   - Check all ground connections for proper contact
   - Verify power supply stability (use capacitors if needed)
   - Inspect sensor wiring for loose connections or shorts
   - Increase CURRENT_NUM_SAMPLES for more stable readings

2. Relay Issues
   - Verify 5V supply to relay module is stable
   - Check GPIO21 output voltage when relay should be active
   - Test relay manually by applying voltage to input pin
   - Inspect relay contacts for wear or damage

3. Communication Problems
   - Position ESP32 for optimal WiFi signal strength
   - Check SERVER_HOST and SERVER_PORT settings
   - Verify API endpoints match your server configuration
   - Increase MAX_HTTP_RETRIES if network is unreliable

4. Fault Detection Issues
   - Review threshold values in config.h
   - Adjust VOLTAGE_LOW_THRESHOLD and VOLTAGE_HIGH_THRESHOLD
   - Fine-tune CURRENT_LOW_THRESHOLD and CURRENT_HIGH_THRESHOLD
   - Check POWER_LOW_THRESHOLD for your specific light fixture

## Maintenance

1. Regular Checks
   - Inspect all connections for tightness monthly
   - Verify sensor calibration quarterly
   - Check relay contacts for wear or pitting
   - Monitor WiFi signal strength and connection stability

2. Cleaning
   - Clean LDR surface to ensure accurate light readings
   - Remove dust from circuit boards using compressed air
   - Clean connector contacts with contact cleaner
   - Inspect enclosure seals for water ingress

3. Preventive Maintenance
   - Replace relay module annually if used in high-switching applications
   - Recalibrate sensors every six months
   - Check wire condition for insulation damage
   - Update firmware with latest improvements

## API Integration

### Data Endpoints
The microcontroller communicates with the server using two main endpoints:

1. Sensor Data Endpoint (`/api/monitoring/sensor-data/`)
   - Sends real-time sensor readings to the server
   - Includes voltage, current, power, and light level data
   - Sent every 30 seconds (configurable via DATA_SEND_INTERVAL_MS)
   - JSON format with the following fields:
     ```json
     {
       "tower_id": 1,
       "sensor_value": 3500,
       "light_level": 85,
       "lux": 1200,
       "faulty_lights": 0,
       "maintenance_lights": 0,
       "efficiency": 92,
       "voltage": 12.1,
       "current": 0.75,
       "power": 9.08
     }
     ```

2. Status Update Endpoint (`/api/tower-status-update/`)
   - Reports tower status changes to the server
   - Sends fault information when detected
   - Sent every 60 seconds (configurable via STATUS_UPDATE_INTERVAL_MS)
   - JSON format with the following fields:
     ```json
     {
       "tower_id": 1,
       "status": "active",
       "fault_type": "undervoltage"  // Only included when status is "faulty"
     }
     ```

### Fault Detection Logic

The microcontroller detects the following fault conditions:

1. Under Voltage: `voltage < VOLTAGE_LOW_THRESHOLD && voltage > 0.5`
   - Indicates power supply issues or wiring problems
   - Threshold configurable in config.h (default: 10.0V)

2. Over Voltage: `voltage > VOLTAGE_HIGH_THRESHOLD`
   - Indicates power supply regulation issues
   - Threshold configurable in config.h (default: 12.5V)

3. Low Current: `relayState && voltage > VOLTAGE_LOW_THRESHOLD && current < CURRENT_LOW_THRESHOLD && current >= 0`
   - Indicates bulb failure or wiring issues
   - Threshold configurable in config.h (default: 0.02A)

4. High Current: `current > CURRENT_HIGH_THRESHOLD`
   - Indicates short circuit or overload
   - Threshold configurable in config.h (default: 2.50A)

5. Low Power: `relayState && voltage > VOLTAGE_LOW_THRESHOLD && current > (CURRENT_LOW_THRESHOLD / 2.0) && power < POWER_LOW_THRESHOLD`
   - Indicates connection issues or partial failures
   - Threshold configurable in config.h (default: 2.0W)

When a fault is detected, the microcontroller:
1. Sets the tower status to "faulty"
2. Identifies the specific fault type
3. Sends a status update to the server
4. Continues monitoring to detect recovery

## Automatic Control Logic

The tower light is automatically controlled based on the following factors:

### Time-Based Control
- Night hours are defined in config.h:
  - Start time: `NIGHT_HOUR_START:NIGHT_MINUTE_START` (default: 18:00)
  - End time: `NIGHT_HOUR_END:NIGHT_MINUTE_END` (default: 06:00)
- The microcontroller uses NTP to synchronize time
- During night hours, the light is turned ON (if no faults are detected)
- Outside night hours, the light is turned OFF

### Light-Based Control
- The LDR sensor measures ambient light levels
- If light level falls below `LIGHT_PERCENT_DARK_THRESHOLD` (default: 20%), the light is turned ON
- This provides backup control if the NTP time sync fails
- Also ensures lights turn on during unusually dark daytime conditions (storms, etc.)

### Fault Response
- If a fault is detected, the tower status is set to "faulty"
- The light may be turned OFF depending on the fault type
- The system continues to monitor conditions and can auto-recover

### Manual Override
- The system supports manual override functionality
- When activated, it overrides automatic control for a set period
- After `MANUAL_CONTROL_TIMEOUT_MS` (default: 5 minutes), automatic control resumes
- This feature is primarily for maintenance and testing purposes