/*
 * RelayLightTest.ino
 * Simple test to verify relay wiring and light-based switching
 * Relay turns ON when light is low, OFF when light is high
 * 
 * For ESP32/Arduino microcontroller
 */

// --- Configuration ---
#define TOWER_ID 1  // Change to 2 for second microcontroller

// Pin Definitions
#define LDR_PIN 33              // Analog pin for LDR (adjust as needed)
#define RELAY_PIN 21             // Digital pin for relay control
#define STATUS_LED_PIN 2  // Built-in LED

// ADC Configuration
#define ADC_MAX_VALUE 4095.0    // Max value for 12-bit ADC (ESP32)
#define ADC_VREF 3.3            // ESP32 ADC reference voltage

// LDR Configuration
#define LDR_RAW_ADC_MIN 500     // Bright light (low resistance)
#define LDR_RAW_ADC_MAX 3500    // Dark (high resistance)

// Light Thresholds
#define LIGHT_DARK_THRESHOLD 30   // Below this % = dark (relay ON)
#define LIGHT_BRIGHT_THRESHOLD 50 // Above this % = bright (relay OFF)
// Hysteresis prevents rapid switching

// Timing
#define READ_INTERVAL_MS 1000     // Read sensors every 1 second
#define DISPLAY_INTERVAL_MS 2000  // Display status every 2 seconds

// --- Global Variables ---
bool relayState = false;
int lightPercent = 0;
int lightRawAdc = 0;
unsigned long lastDisplayTime = 0;

// --- Helper Functions ---
float mapFloat(float x, float in_min, float in_max, float out_min, float out_max) {
  if (in_max == in_min) {
    return out_min;
  }
  return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min;
}

void controlRelay(bool state) {
  // Active-Low relay: LOW = ON, HIGH = OFF
  digitalWrite(RELAY_PIN, state ? LOW : HIGH);
  
  if (relayState != state) {
    relayState = state;
    Serial.printf("🔌 RELAY: %s (Pin: %s)\n", 
                  state ? "ON" : "OFF", 
                  state ? "LOW" : "HIGH");
    Serial.flush();
    
    // Blink LED to indicate relay change
    blinkStatusLed(state ? 3 : 1);
  }
}

void blinkStatusLed(int times) {
  for (int i = 0; i < times; i++) {
    digitalWrite(STATUS_LED_PIN, HIGH);
    delay(100);
    digitalWrite(STATUS_LED_PIN, LOW);
    delay(100);
  }
}

void readLightSensor() {
  // Read raw ADC value
  lightRawAdc = analogRead(LDR_PIN);
  
  // Convert to percentage (0% = dark, 100% = bright)
  lightPercent = (int)mapFloat(lightRawAdc, LDR_RAW_ADC_MAX, LDR_RAW_ADC_MIN, 0, 100);
  lightPercent = constrain(lightPercent, 0, 100);
}

void updateRelayBasedOnLight() {
  if (relayState) {
    // Relay is currently ON, turn OFF only if light gets bright
    if (lightPercent > LIGHT_BRIGHT_THRESHOLD) {
      controlRelay(false);
      Serial.printf("💡 Light is bright (%d%%) - Turning relay OFF\n", lightPercent);
      Serial.flush();
    }
  } else {
    // Relay is currently OFF, turn ON only if light gets dark
    if (lightPercent < LIGHT_DARK_THRESHOLD) {
      controlRelay(true);
      Serial.printf("🌙 Light is dark (%d%%) - Turning relay ON\n", lightPercent);
      Serial.flush();
    }
  }
}

void displayStatus() {
  unsigned long currentTime = millis();
  if (currentTime - lastDisplayTime >= DISPLAY_INTERVAL_MS) {
    Serial.println("\n============================================================");
    Serial.printf("🏗️  TOWER %d - RELAY LIGHT TEST\n", TOWER_ID);
    Serial.println("============================================================");
    Serial.printf("📊 RAW ADC:      %6d / %5d (%5.1f%%)\n", 
                   lightRawAdc, (int)ADC_MAX_VALUE, (lightRawAdc/ADC_MAX_VALUE)*100);
    
    String lightStatus;
    if (lightPercent < LIGHT_DARK_THRESHOLD) {
      lightStatus = "DARK";
    } else if (lightPercent > LIGHT_BRIGHT_THRESHOLD) {
      lightStatus = "BRIGHT";
    } else {
      lightStatus = "MEDIUM";
    }
    
    Serial.printf("💡 LIGHT LEVEL:  %3d%% (%s)\n", lightPercent, lightStatus.c_str());
    Serial.printf("🔌 RELAY STATE:  %s (Pin: %s)\n", 
                   relayState ? "ON" : "OFF", 
                   relayState ? "LOW" : "HIGH");
    Serial.printf("⚙️  THRESHOLDS:   Dark < %d%% | Bright > %d%%\n", 
                   LIGHT_DARK_THRESHOLD, LIGHT_BRIGHT_THRESHOLD);
    
    // Visual light bar
    Serial.print("📈 LIGHT BAR:    |");
    int barLength = 20;
    int filled = (lightPercent * barLength) / 100;
    for (int i = 0; i < barLength; i++) {
      if (i < filled) {
        Serial.print("█");
      } else {
        Serial.print("░");
      }
    }
    Serial.printf("| %d%%\n", lightPercent);
    
    Serial.println("============================================================");
    Serial.flush();
    lastDisplayTime = currentTime;
  }
}

void testRelayManual() {
  Serial.println("\n🧪 MANUAL RELAY TEST - 10 cycles");
  Serial.println("Watch the relay and any connected lights...");
  Serial.flush();
  
  for (int i = 0; i < 10; i++) {
    bool state = (i % 2 == 0);  // Alternate ON/OFF
    controlRelay(state);
    Serial.printf("Cycle %d/10: Relay %s\n", i+1, state ? "ON" : "OFF");
    Serial.flush();
    delay(3000);
  }
  
  controlRelay(false);  // Ensure OFF at end
  Serial.println("✅ Manual test complete\n");
  Serial.flush();
}

void setup() {
  Serial.begin(115200);
  delay(1000);  // Wait for serial to initialize
  
  Serial.println("\n🚀 STARTING RELAY LIGHT TEST");
  Serial.printf("Tower ID: %d\n", TOWER_ID);
  Serial.println("============================================================");
  Serial.println("📋 TEST DESCRIPTION:");
  Serial.printf("• Relay turns ON when light < %d%%\n", LIGHT_DARK_THRESHOLD);
  Serial.printf("• Relay turns OFF when light > %d%%\n", LIGHT_BRIGHT_THRESHOLD);
  Serial.println("• Hysteresis prevents rapid switching");
  Serial.println("• Active-LOW relay logic (LOW = ON, HIGH = OFF)");
  Serial.println("============================================================");
  Serial.flush();
  
  // Initialize pins
  pinMode(RELAY_PIN, OUTPUT);
  pinMode(STATUS_LED_PIN, OUTPUT);
  
  // Start with relay OFF (HIGH for active-low)
  digitalWrite(RELAY_PIN, HIGH);
  digitalWrite(STATUS_LED_PIN, LOW);
  
  // Optional: Run manual test first
  Serial.println("Send 'y' within 5 seconds to run manual relay test first...");
  Serial.flush();
  
  unsigned long startTime = millis();
  bool runManualTest = false;
  
  while (millis() - startTime < 5000) {
    if (Serial.available()) {
      String input = Serial.readString();
      input.trim();
      if (input.equalsIgnoreCase("y")) {
        runManualTest = true;
        break;
      }
    }
    delay(100);
  }
  
  if (runManualTest) {
    testRelayManual();
  }
  
  Serial.println("🔄 Starting automatic light-based control...");
  Serial.println("💡 Cover/uncover the LDR sensor to test switching");
  Serial.println("🛑 Send any character to stop\n");
  Serial.flush();
}

void loop() {
  // Check for stop command
  if (Serial.available()) {
    Serial.println("\n🛑 Test stopped by user");
    controlRelay(false);  // Ensure relay is OFF
    Serial.println("✅ Relay turned OFF - Test complete");
    Serial.flush();
    while (true) {
      delay(1000);  // Stop the loop
    }
  }
  
  // Read light sensor
  readLightSensor();
  
  // Update relay based on light
  updateRelayBasedOnLight();
  
  // Display status
  displayStatus();
  
  // Wait before next reading
  delay(READ_INTERVAL_MS);
}
