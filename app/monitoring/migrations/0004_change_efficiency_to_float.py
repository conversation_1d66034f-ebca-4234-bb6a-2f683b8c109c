# Generated by Django 4.2.11 on 2025-06-08 12:55

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0003_sensordata_current_sensordata_power_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sensordata',
            name='efficiency',
            field=models.FloatField(help_text='Efficiency percentage (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)]),
        ),
    ]
