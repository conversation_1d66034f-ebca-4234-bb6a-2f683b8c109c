# Tower Light Monitoring and Fault Detection System

## Overview

The Tower Light Monitoring and Fault Detection System is a comprehensive solution for monitoring, controlling, and managing tower lights in real-time. The system consists of a web application built with Django and a network of ESP32 microcontrollers that collect and transmit sensor data. The system provides real-time visualization of tower light status, automated fault detection, and maintenance scheduling.

## Features

- **Real-time Monitoring**: Monitor voltage, current, power consumption, and light levels of tower lights in real-time
- **Fault Detection**: Automatically detect and report faults in tower lights
- **Smart Control**: Automatically activate tower lights based on light levels and time of day
- **Interactive Dashboard**: Visualize system status, alerts, and performance metrics
- **Tower Light Cards**: View detailed information for each tower light with modern circular gauges
- **Map Integration**: View tower light locations on an interactive map
- **Maintenance Scheduling**: Schedule and track maintenance activities
- **Alert System**: Receive notifications for faults and maintenance needs
- **Mobile Responsive**: Access the system from any device with a responsive design

## System Architecture

### Web Application (Backend)

- **Framework**: Django 4.2
- **Database**: SQLite (development), PostgreSQL (production)
- **API**: REST API built with Django REST Framework
- **Authentication**: Django's built-in authentication system

### Web Application (Frontend)

- **UI Framework**: Tailwind CSS
- **JavaScript**: Vanilla JavaScript with Fetch API
- **Visualization**: Custom-built circular gauges and charts
- **Maps**: Leaflet.js for interactive maps

### Microcontroller

- **Hardware**: ESP32 development boards
- **Sensors**:
  - Voltage sensor for measuring power supply voltage
  - Current sensor (ACS712) for measuring current consumption
  - LDR (Light Dependent Resistor) for measuring ambient light levels
- **Connectivity**: WiFi for communication with the web application
- **Power**: 5V DC power supply

## System Components

### 1. Dashboard

The dashboard provides an overview of the entire system, including:

- Total number of tower lights
- Number of active, faulty, and maintenance-required tower lights
- System efficiency
- Recent alerts
- Interactive map showing tower light locations and status
- Performance metrics and charts

### 2. Tower Lights Page

The Tower Lights page displays detailed information for each tower light in a card-based layout:

- Real-time voltage, current, power, and light level displayed as circular gauges
- Status indicator (active, faulty, maintenance)
- Color-coded cards based on status (blue for active, red for faulty, yellow for maintenance)
- Geographic coordinates
- Last update timestamp
- Automatic updates every second

### 3. Control Panel

The Control Panel allows administrators to:

- Manually control tower lights (turn on/off)
- Set operating parameters
- Configure automatic operation based on light levels and time
- Override automatic settings for special events

### 4. Alerts Page

The Alerts page displays all system alerts, including:

- Fault alerts (power issues, bulb failures, connection problems)
- Maintenance alerts
- System notifications
- Alert history and resolution status

### 5. Tower Detail Page

The Tower Detail page provides comprehensive information about a specific tower light:

- Historical data charts
- Maintenance history
- Fault history
- Configuration settings
- Sensor readings over time

## Database Schema

### TowerLight Model

- `id`: Primary key
- `name`: Tower light name
- `status`: Current status (active, inactive, faulty, maintenance)
- `latitude`: Geographic latitude
- `longitude`: Geographic longitude
- `installation_date`: Date of installation
- `last_maintenance`: Date of last maintenance

### SensorData Model

- `id`: Primary key
- `tower`: Foreign key to TowerLight
- `timestamp`: Time of data collection
- `sensor_value`: Raw sensor value
- `light_level`: Light level percentage (0-100)
- `faulty_lights`: Number of faulty lights
- `maintenance_lights`: Number of lights needing maintenance
- `efficiency`: Efficiency percentage (0-100)
- `voltage`: Voltage reading in volts
- `current`: Current reading in amperes
- `power`: Power consumption in watts

### FaultLog Model

- `id`: Primary key
- `tower`: Foreign key to TowerLight
- `timestamp`: Time of fault detection
- `fault_type`: Type of fault (power, bulb, connection)
- `description`: Detailed description of the fault
- `resolved`: Boolean indicating if the fault is resolved
- `resolved_at`: Time of resolution

### MaintenanceSchedule Model

- `id`: Primary key
- `tower`: Foreign key to TowerLight
- `scheduled_date`: Scheduled maintenance date
- `description`: Description of maintenance task
- `completed`: Boolean indicating if maintenance is completed
- `completed_at`: Time of completion

## API Endpoints

### 1. Sensor Data Endpoint

**URL:** `/api/monitoring/sensor-data/`
**Method:** POST
**Purpose:** Receive sensor data from microcontrollers

**Required Parameters:**
- `tower_id`: Tower light ID
- `sensor_value`: Raw sensor value
- `light_level`: Light level percentage

**Optional Parameters:**
- `faulty_lights`: Number of faulty lights
- `maintenance_lights`: Number of lights needing maintenance
- `efficiency`: Efficiency percentage
- `voltage`: Voltage reading
- `current`: Current reading
- `power`: Power consumption

### 2. Tower Status Update Endpoint

**URL:** `/api/tower-status-update/`
**Method:** POST
**Purpose:** Update tower light status

**Required Parameters:**
- `tower_id`: Tower light ID
- `status`: Tower light status (active, inactive, faulty, maintenance)

**Optional Parameters:**
- `fault_type`: Type of fault if status is "faulty"

### 3. Dashboard Data Endpoint

**URL:** `/api/dashboard-data/`
**Method:** GET
**Purpose:** Retrieve dashboard data

### 4. Tower Data Endpoint

**URL:** `/api/tower-data/`
**Method:** GET
**Purpose:** Retrieve data for all tower lights with pagination

**Optional Parameters:**
- `page`: Page number for pagination
- `per_page`: Number of towers per page

## Microcontroller Code

The ESP32 microcontroller code performs the following functions:

1. **Sensor Reading**:
   - Reads voltage from the voltage sensor
   - Reads current from the ACS712 current sensor
   - Reads light level from the LDR sensor
   - Calculates power consumption

2. **Data Processing**:
   - Converts raw sensor values to meaningful units
   - Detects faults based on sensor readings
   - Determines tower light status

3. **Communication**:
   - Connects to WiFi network
   - Sends sensor data to the web application
   - Updates tower light status
   - Handles connection errors and retries

4. **Control Logic**:
   - Turns tower light on/off based on light level and time of day
   - Implements smart lighting behavior
   - Responds to status changes

5. **Simulation**:
   - Generates realistic data for testing
   - Simulates multiple tower lights for development

## Installation and Setup

### Web Application

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/towerlight-monitoring-system.git
   cd towerlight-monitoring-system
   ```

2. Create a virtual environment and install dependencies:
   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. Apply database migrations:
   ```
   python manage.py migrate
   ```

4. Create a superuser:
   ```
   python manage.py createsuperuser
   ```

5. Run the development server:
   ```
   python manage.py runserver
   ```

6. Access the application at http://127.0.0.1:8000/

### Microcontroller

1. Install the Arduino IDE and ESP32 board support
2. Install required libraries:
   - WiFi
   - HTTPClient
   - ArduinoJson
   - Wire
3. Open the `esp32_tower_light_controller.ino` file
4. Configure WiFi credentials and server address
5. Upload the code to the ESP32 board

## Hardware Setup

### Components Required per Tower Light

- ESP32 development board
- Voltage sensor module
- ACS712 current sensor
- LDR (Light Dependent Resistor)
- 10kΩ resistor for LDR
- Tower light with RGB indicators (or simulation LEDs)
- Breadboard and jumper wires
- 5V power supply

### Wiring Diagram

- Connect the voltage sensor to pin A0 (GPIO 35)
- Connect the ACS712 current sensor to pin A1 (GPIO 32)
- Connect the LDR and 10kΩ resistor in voltage divider configuration to pin A2 (GPIO 33)
- Connect red LED (fault indicator) to pin D12 (GPIO 12)
- Connect yellow LED (maintenance indicator) to pin D14 (GPIO 14)
- Connect green LED (active indicator) to pin D27 (GPIO 27)
- Connect built-in LED (connectivity indicator) to pin D2 (GPIO 2)

## Usage

### Web Application

1. **Dashboard**: Access the dashboard at `/dashboard/` to view system overview
2. **Tower Lights**: View all tower lights at `/tower-lights/`
3. **Control Panel**: Control tower lights at `/control/`
4. **Alerts**: View system alerts at `/alerts/`
5. **Tower Detail**: View detailed information for a specific tower at `/tower/{id}/`

### Microcontroller

The microcontroller automatically:

1. Connects to the configured WiFi network
2. Reads sensor data at regular intervals
3. Sends data to the web application
4. Updates tower light status based on sensor readings
5. Controls the tower light based on light level and time of day

## Smart Lighting Behavior

The system implements smart lighting behavior:

1. **Time-based Control**: Tower lights automatically turn on from 18:00 to 06:00 (GMT+2 Harare time)
2. **Light Level Control**: Tower lights turn on when ambient light level falls below 20%
3. **Fault Detection**: System detects faults based on:
   - Voltage too low (< 20V) when the light should be on
   - Current too low (< 1A) when voltage is good (bulb issue)
   - Power calculation discrepancy (connection issue)
4. **Maintenance Detection**: System identifies maintenance needs based on efficiency:
   - Efficiency < 70% and >= 50%: One light needs maintenance
   - Efficiency < 50%: Two lights need maintenance

## Development and Customization

### Adding New Features

1. Create new Django app or extend existing apps
2. Add models to `models.py`
3. Create views in `views.py`
4. Add URL patterns in `urls.py`
5. Create templates in the `templates` directory
6. Add static files in the `static` directory

### Customizing the UI

1. Modify the Tailwind CSS configuration in `tailwind.config.js`
2. Edit the base template in `templates/base.html`
3. Customize component templates in their respective directories

### Extending the API

1. Create new serializers in `serializers.py`
2. Add API views in `views.py`
3. Register URL patterns in `urls.py`

## Troubleshooting

### Web Application Issues

- **Database Errors**: Run `python manage.py migrate` to apply migrations
- **Static Files Not Loading**: Run `python manage.py collectstatic`
- **Permission Errors**: Check user permissions and authentication
- **API Errors**: Check the server logs for detailed error messages

### Microcontroller Issues

- **WiFi Connection Failures**: Verify WiFi credentials and signal strength
- **Sensor Reading Errors**: Check sensor connections and calibration
- **API Communication Errors**: Verify server address and endpoint URLs
- **Power Issues**: Ensure stable power supply to the ESP32

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Django framework and community
- ESP32 development community
- Tailwind CSS team
- All contributors to the project