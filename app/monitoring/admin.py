from django.contrib import admin
from django.utils import timezone
from .models import Tower<PERSON>ight, FaultLog, Technician, MaintenanceSchedule, SensorData, TowerCommand

# This class customizes how the TowerLight model is displayed in the admin.
@admin.register(TowerLight)
class TowerLightAdmin(admin.ModelAdmin):
    """
    Admin view for the TowerLight model.
    """
    list_display = ('name', 'status', 'location_display', 'last_checked')
    list_filter = ('status',)
    search_fields = ('name', 'description')

    def location_display(self, obj):
        return f"({obj.latitude}, {obj.longitude})"
    location_display.short_description = "Location (Lat, Lng)"

# This class customizes the display for FaultLog entries.
@admin.register(FaultLog)
class FaultLogAdmin(admin.ModelAdmin):
    """
    Admin view for the FaultLog model.
    """
    list_display = ('timestamp', 'tower', 'fault_type', 'resolved', 'resolved_at', 'resolved_by')
    list_filter = ('resolved', 'fault_type', 'tower__name')
    search_fields = ('tower__name', 'description')
    actions = ['mark_as_resolved']

    def mark_as_resolved(self, request, queryset):
        """Custom admin action to mark selected faults as resolved."""
        queryset.update(
            resolved=True,
            resolved_at=timezone.now(),
            resolved_by=request.user
        )
    mark_as_resolved.short_description = "Mark selected faults as resolved"

# This class customizes the display for Technician entries.
@admin.register(Technician)
class TechnicianAdmin(admin.ModelAdmin):
    """
    Admin view for the Technician model.
    """
    list_display = ('get_full_name', 'phone_number', 'region', 'is_supervisor')
    search_fields = ('user__username', 'user__first_name', 'user__last_name', 'region')
    list_filter = ('region', 'is_supervisor')

    def get_full_name(self, obj):
        return obj.user.get_full_name() or obj.user.username
    get_full_name.short_description = 'Technician Name'


# This class customizes the display for MaintenanceSchedule entries.
@admin.register(MaintenanceSchedule)
class MaintenanceScheduleAdmin(admin.ModelAdmin):
    """
    Admin view for the MaintenanceSchedule model.
    """
    list_display = ('scheduled_date', 'tower', 'technician', 'completed')
    list_filter = ('completed', 'technician', 'scheduled_date')
    search_fields = ('tower__name', 'technician__user__username', 'notes')

# This class customizes the display for SensorData entries.
@admin.register(SensorData)
class SensorDataAdmin(admin.ModelAdmin):
    """
    Admin view for the SensorData model.
    Provides a detailed, read-only view of incoming sensor data.
    """
    # Fields to display in the main list view.
    list_display = (
        'timestamp',
        'tower',
        'voltage',
        'current',
        'power',
        'light_level',
        'efficiency',
        'faulty_lights'
    )

    # Add filters to the sidebar to easily find data.
    list_filter = ('tower', 'timestamp', 'faulty_lights')

    # Allows searching for data associated with a specific tower.
    search_fields = ('tower__name',)

    # Make all fields read-only since this data comes from the Pico.
    readonly_fields = [field.name for field in SensorData._meta.get_fields()]

    def has_add_permission(self, request):
        # Prevent users from manually adding new sensor data via the admin.
        return False

    def has_delete_permission(self, request, obj=None):
        # Optionally, prevent deletion of historical data.
        return False

@admin.register(TowerCommand)
class TowerCommandAdmin(admin.ModelAdmin):
    list_display = ['tower', 'command_type', 'status', 'created_at', 'sent_at', 'completed_at', 'created_by']
    list_filter = ['command_type', 'status', 'tower', 'created_at']
    search_fields = ['tower__name', 'command_type', 'created_by__username']
    readonly_fields = ['created_at', 'sent_at', 'acknowledged_at', 'completed_at']
    ordering = ['-created_at']

    fieldsets = (
        ('Command Info', {
            'fields': ('tower', 'command_type', 'parameters', 'created_by')
        }),
        ('Status', {
            'fields': ('status', 'response_data', 'error_message')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'sent_at', 'acknowledged_at', 'completed_at', 'expires_at')
        }),
    )

