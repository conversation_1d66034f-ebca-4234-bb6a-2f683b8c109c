<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Tower Light Monitoring{% endblock %}</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: {
                            DEFAULT: '#3D8FCA',
                            dark: '#2C7AB3',
                            light: '#5BA3D5'
                        },
                        secondary: {
                            DEFAULT: '#FF574D',
                            dark: '#E54D44',
                            light: '#FF7A72'
                        },
                        warning: {
                            DEFAULT: '#FFC85C',
                            dark: '#E5B652',
                            light: '#FFD47F'
                        },
                        background: {
                            DEFAULT: '#1E2023',
                            light: '#2A2D31',
                            dark: '#17191C'
                        },
                        accent: {
                            DEFAULT: '#2C2F33',
                            light: '#383B40',
                            dark: '#212326'
                        },
                        surface: {
                            DEFAULT: '#34373C',
                            light: '#3E4148',
                            dark: '#2A2D31'
                        }
                    },
                    animation: {
                        'gradient': 'gradient 8s linear infinite',
                        'fade-in': 'fadeIn 0.5s ease-out',
                        'slide-up': 'slideUp 0.5s ease-out',
                    },
                    keyframes: {
                        gradient: {
                            '0%, 100%': {
                                'background-size': '200% 200%',
                                'background-position': 'left center'
                            },
                            '50%': {
                                'background-size': '200% 200%',
                                'background-position': 'right center'
                            }
                        },
                        fadeIn: {
                            '0%': { opacity: '0' },
                            '100%': { opacity: '1' }
                        },
                        slideUp: {
                            '0%': { transform: 'translateY(20px)', opacity: '0' },
                            '100%': { transform: 'translateY(0)', opacity: '1' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        .gradient-card {
            background: linear-gradient(135deg, var(--tw-gradient-from) 0%, var(--tw-gradient-to) 100%);
            transition: all 0.3s ease;
        }
        .gradient-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0,0,0,0.2);
        }
        .glass-effect {
            backdrop-filter: blur(12px);
            background: rgba(255, 255, 255, 0.1);
        }

        /* Status Badge Styles */
        .status-badge {
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: capitalize;
        }

        .status-green {
            background-color: rgba(34, 197, 94, 0.2);
            color: rgb(74, 222, 128);
            border: 1px solid rgba(34, 197, 94, 0.3);
        }

        .status-red {
            background-color: rgba(239, 68, 68, 0.2);
            color: rgb(248, 113, 113);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .status-orange {
            background-color: rgba(245, 158, 11, 0.2);
            color: rgb(251, 191, 36);
            border: 1px solid rgba(245, 158, 11, 0.3);
        }

        .status-gray {
            background-color: rgba(107, 114, 128, 0.2);
            color: rgb(156, 163, 175);
            border: 1px solid rgba(107, 114, 128, 0.3);
        }

        /* Status Indicator Styles */
        .status-indicator {
            width: 0.75rem;
            height: 0.75rem;
            border-radius: 9999px;
        }

        .status-indicator.status-green {
            background-color: rgb(74, 222, 128);
            box-shadow: 0 0 8px rgba(74, 222, 128, 0.5);
        }

        .status-indicator.status-red {
            background-color: rgb(248, 113, 113);
            box-shadow: 0 0 8px rgba(248, 113, 113, 0.5);
        }

        .status-indicator.status-orange {
            background-color: rgb(251, 191, 36);
            box-shadow: 0 0 8px rgba(251, 191, 36, 0.5);
        }

        .status-indicator.status-gray {
            background-color: rgb(156, 163, 175);
            box-shadow: 0 0 8px rgba(156, 163, 175, 0.5);
        }
    </style>
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-background text-gray-100">
<div class="flex h-screen">
    <!-- Sidebar -->
    <aside class="w-64 bg-surface shadow-lg">
        <div class="p-4 border-b border-accent">
            <h1 class="text-xl font-bold text-white">Towerlighting</h1>
        </div>
        <nav class="p-4">
            <ul class="space-y-2">
                <li>
                    <a href="{% url 'monitoring:dashboard' %}" class="flex items-center p-2 text-gray-300 hover:bg-accent rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>
                        </svg>
                        Dashboard
                    </a>
                </li>
                <li>
                    <a href="{% url 'monitoring:alerts' %}" class="flex items-center p-2 text-gray-300 hover:bg-accent rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                        </svg>
                        Alerts
                    </a>
                </li>
                <li>
                    <a href="{% url 'monitoring:control' %}" class="flex items-center p-2 text-gray-300 hover:bg-accent rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 110-4m0 4v2m0-6V4"/>
                        </svg>
                        Control Panel
                    </a>
                </li>
                <li>
                    <a href="{% url 'monitoring:tower_lights' %}" class="flex items-center p-2 text-gray-300 hover:bg-accent rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                        Tower Lights
                    </a>
                </li>
                <li>
                    <a href="{% url 'monitoring:clear_alerts' %}" class="flex items-center p-2 text-gray-300 hover:bg-accent rounded-lg transition-all duration-200">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                        </svg>
                        Clear Alerts
                    </a>
                </li>
            </ul>
        </nav>
    </aside>

    <!-- Main Content -->
    <div class="flex-1 flex flex-col overflow-hidden">
        <!-- Top Navigation -->
        <header class="bg-surface shadow-sm">
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-4">
                    <input type="text" placeholder="Search..." class="px-4 py-2 bg-accent border-0 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary text-gray-300 placeholder-gray-500">
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 hover:bg-accent rounded-lg transition-colors duration-200">
                        <svg class="w-5 h-5 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                        </svg>
                    </button>
                    <div class="flex items-center space-x-4">
                        {% if user.is_authenticated %}
                            <span class="text-gray-300">{{ user.username }}</span>
                            <a href="{% url 'logout' %}" class="bg-secondary hover:bg-secondary-dark text-white font-bold py-2 px-4 rounded-lg transition duration-200">
                                Logout
                            </a>
                        {% else %}
                            <a href="{% url 'login' %}" class="text-primary hover:text-primary-light transition duration-200">Login</a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </header>

        <!-- Main Content Area -->
        <main class="flex-1 overflow-x-hidden overflow-y-auto bg-background p-6">
            {% block content %}{% endblock %}
        </main>
    </div>
</div>

<script>
    // Add smooth transitions for all interactive elements
    document.addEventListener('DOMContentLoaded', () => {
        const cards = document.querySelectorAll('.gradient-card');
        cards.forEach(card => {
            card.addEventListener('mouseover', () => {
                card.style.transform = 'translateY(-2px)';
            });
            card.addEventListener('mouseout', () => {
                card.style.transform = 'translateY(0)';
            });
        });
    });
</script>
{% block extra_js %}{% endblock %}

<!-- Global Alert Modal -->
<div id="global-alert-modal" class="fixed inset-0 z-50 flex items-center justify-center hidden">
    <div id="alert-modal-bg" class="absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm"></div>
    <div id="alert-modal-content" class="relative bg-surface rounded-lg shadow-xl p-6 max-w-md w-full border-4 transform transition-all duration-300 ease-out overflow-hidden">
        <div class="flex items-center mb-4">
            <span id="alert-modal-icon" class="mr-3 text-3xl">⚠️</span>
            <h2 id="alert-modal-title" class="text-xl font-bold text-white"></h2>
        </div>
        <div id="alert-modal-body" class="text-gray-300 mb-4"></div>
        <div class="mt-6">
            <div class="text-xs text-gray-400 mb-2 text-center">
                Dismissing this alert will hide it for 5 minutes if the fault persists
            </div>
            <div class="text-xs text-gray-500 mb-2 text-center italic">
                Note: Alerts are only shown for Tower Light 1 and Tower Light 2
            </div>
            <div class="flex justify-end space-x-3">
                <button id="alert-modal-dismiss" class="px-4 py-2 bg-gray-700 text-gray-200 rounded-lg hover:bg-gray-600 transition-colors duration-200 font-medium">
                    Dismiss
                </button>
                <a id="alert-modal-details" href="#" class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 font-medium shadow-lg hover:shadow-xl">
                    View Details
                </a>
            </div>
        </div>
    </div>
</div>

<script>
// Alert Modal System
const ALERT_POLL_INTERVAL = 10000; // 10 seconds
const ALERT_RESHOW_DELAY = 5 * 60 * 1000; // 5 minutes (300 seconds)

// Initialize dismissed alerts from localStorage or create empty object
let dismissedAlerts = {};

// Load dismissed alerts from localStorage if available
try {
    const savedAlerts = localStorage.getItem('dismissedAlerts');
    if (savedAlerts) {
        dismissedAlerts = JSON.parse(savedAlerts);

        // Clean up expired entries (older than ALERT_RESHOW_DELAY)
        const now = Date.now();
        let cleanupCount = 0;

        Object.keys(dismissedAlerts).forEach(key => {
            if (now - dismissedAlerts[key] > ALERT_RESHOW_DELAY) {
                delete dismissedAlerts[key];
                cleanupCount++;
            }
        });

        if (cleanupCount > 0) {
            console.log(`Cleaned up ${cleanupCount} expired alert dismissals`);
            // Save the cleaned up object back to localStorage
            localStorage.setItem('dismissedAlerts', JSON.stringify(dismissedAlerts));
        }
    }
} catch (e) {
    console.error('Error loading dismissed alerts from localStorage:', e);
    dismissedAlerts = {};
}

function showAlertModal(alert) {
    // Store the current alert globally so it can be accessed by the dismiss functions
    currentAlert = alert;

    const modal = document.getElementById('global-alert-modal');
    const content = document.getElementById('alert-modal-content');
    const icon = document.getElementById('alert-modal-icon');
    const title = document.getElementById('alert-modal-title');
    const body = document.getElementById('alert-modal-body');
    const detailsBtn = document.getElementById('alert-modal-details');

    // Color coding based on alert level
    content.classList.remove('border-red-500', 'border-yellow-500', 'border-orange-500');

    // Remove any existing animation classes
    icon.classList.remove('animate-pulse');

    if (alert.level === 'critical') {
        content.classList.add('border-red-500');
        icon.textContent = '⛔';
        icon.className = 'mr-3 text-3xl text-red-500';
        // Add pulse animation for critical alerts
        icon.classList.add('animate-pulse');
    } else if (alert.fault_type === 'maintenance') {
        content.classList.add('border-orange-500');
        icon.textContent = '🔧';
        icon.className = 'mr-3 text-3xl text-orange-400';
    } else {
        content.classList.add('border-yellow-500');
        icon.textContent = '⚠️';
        icon.className = 'mr-3 text-3xl text-yellow-400';
    }

    // Set title with fault type
    const faultTypeDisplay = {
        'power': 'Power Failure',
        'bulb': 'Bulb Failure',
        'connection': 'Connection Issue',
        'other': 'Unknown Issue',
        'maintenance': 'Maintenance Required'
    };

    const faultName = faultTypeDisplay[alert.fault_type] || alert.fault_type.replace(/^\w/, c => c.toUpperCase());
    title.textContent = `${faultName} at ${alert.tower_name}`;

    // Set body content with more details
    body.innerHTML = `
        <div class="space-y-3">
            <div class="p-2 rounded ${alert.level === 'critical' ? 'bg-red-900/30' : 'bg-yellow-900/30'}">
                <strong class="text-${alert.level === 'critical' ? 'red' : 'yellow'}-400">Alert Level:</strong>
                <span class="font-semibold text-${alert.level === 'critical' ? 'red' : 'yellow'}-300">
                    ${alert.level.toUpperCase()}
                </span>
            </div>
            <div class="p-2 bg-surface-light rounded">
                <strong class="block mb-1">Description:</strong>
                <p class="text-gray-200">${alert.description || 'No description available'}</p>
            </div>
            <div class="text-sm text-gray-400 mt-2"><strong>Time:</strong> ${alert.timestamp}</div>
        </div>
    `;

    // Set the details link with the correct URL pattern
    detailsBtn.href = `/alert/${alert.id}/`;

    // Store the alert ID so we can dismiss it when the details button is clicked
    detailsBtn.setAttribute('data-alert-id', alert.id);

    // Show modal with animation
    modal.classList.remove('hidden');
    content.style.transform = 'scale(0.95)';
    setTimeout(() => {
        content.style.transform = 'scale(1)';
    }, 50);
}

function hideAlertModal() {
    const modal = document.getElementById('global-alert-modal');
    const content = document.getElementById('alert-modal-content');

    // Hide with animation
    content.style.transform = 'scale(0.95)';
    setTimeout(() => {
        modal.classList.add('hidden');
        content.style.transform = 'scale(1)';
    }, 200);
}

function fetchActiveAlerts() {
    console.log('Fetching active alerts...');
    fetch('/api/active-alerts/')
        .then(res => {
            if (!res.ok) {
                throw new Error(`HTTP error! Status: ${res.status}`);
            }
            return res.json();
        })
        .then(data => {
            console.log(`Received ${data.count} active alerts`);

            // Filter alerts to only show for Tower Light 1 and Tower Light 2
            const filteredAlerts = data.alerts ? data.alerts.filter(alert => {
                return alert.tower_id === 1 || alert.tower_id === 2;
            }) : [];

            console.log(`Filtered to ${filteredAlerts.length} alerts for Tower Light 1 and 2`);

            if (filteredAlerts.length > 0) {
                // Find the first alert not dismissed in the last 3 minutes
                const now = Date.now();

                // Create a set of active alert keys in the format "tower_{id}_fault_{type}"
                const activeAlertKeys = new Set();
                filteredAlerts.forEach(alert => {
                    const alertKey = `tower_${alert.tower_id}_fault_${alert.fault_type}`;
                    activeAlertKeys.add(alertKey);
                });

                // Clean up dismissed alerts that are no longer active or have expired
                let cleanupCount = 0;
                Object.keys(dismissedAlerts).forEach(key => {
                    // Remove if the alert is no longer active or if the dismissal period has expired
                    if (!activeAlertKeys.has(key) || (now - dismissedAlerts[key] > ALERT_RESHOW_DELAY)) {
                        delete dismissedAlerts[key];
                        cleanupCount++;
                    }
                });

                if (cleanupCount > 0) {
                    console.log(`Cleaned up ${cleanupCount} dismissed alerts that are resolved or expired`);
                    // Save the cleaned up object back to localStorage
                    localStorage.setItem('dismissedAlerts', JSON.stringify(dismissedAlerts));
                }

                // Find the first alert that hasn't been dismissed recently
                const persistentAlert = filteredAlerts.find(alert => {
                    // Create the composite key for this alert
                    const alertKey = `tower_${alert.tower_id}_fault_${alert.fault_type}`;

                    // Check if this specific tower+fault combination has been dismissed
                    const lastDismissed = dismissedAlerts[alertKey];

                    // Only show if not dismissed or if dismissal period has expired
                    return !lastDismissed || (now - lastDismissed > ALERT_RESHOW_DELAY);
                });

                if (persistentAlert) {
                    console.log(`Showing alert for ${persistentAlert.tower_name}: ${persistentAlert.fault_type}`);
                    showAlertModal(persistentAlert);
                } else {
                    console.log('All alerts have been recently dismissed');
                    hideAlertModal();
                }
            } else {
                console.log('No active alerts found');
                hideAlertModal();
                // Clear dismissed alerts when there are no active alerts
                dismissedAlerts = {};
            }
        })
        .catch(error => {
            console.error('Error fetching alerts:', error);
            // Don't hide existing alerts on fetch error
        });
}

// Function to dismiss an alert
function dismissAlert(alert) {
    if (!alert) return hideAlertModal();

    // Create a composite key that includes both tower ID and fault type
    // This ensures we don't show the same type of alert for the same tower
    // within the dismissal period
    const towerId = alert.tower_id;
    const faultType = alert.fault_type;
    const alertKey = `tower_${towerId}_fault_${faultType}`;

    // Store the dismissal time
    const now = Date.now();
    dismissedAlerts[alertKey] = now;

    // Calculate when this alert can reappear
    const reappearTime = new Date(now + ALERT_RESHOW_DELAY);
    const timeString = reappearTime.toLocaleTimeString();

    console.log(`Dismissed ${faultType} alert for tower ${alert.tower_name} - will not show again until ${timeString}`);

    // Save to localStorage for persistence between page refreshes
    try {
        localStorage.setItem('dismissedAlerts', JSON.stringify(dismissedAlerts));
    } catch (e) {
        console.error('Error saving dismissed alerts to localStorage:', e);
    }

    hideAlertModal();
}

// Global variable to store the currently displayed alert
let currentAlert = null;

// Initialize event listeners when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Dismiss button event listener
    document.getElementById('alert-modal-dismiss').addEventListener('click', () => {
        // Dismiss the current alert
        dismissAlert(currentAlert);
    });

    // View Details button event listener - also dismisses the alert
    document.getElementById('alert-modal-details').addEventListener('click', () => {
        // Dismiss the current alert
        dismissAlert(currentAlert);
        // The browser will follow the href link after this function completes
    });

    // Close modal when clicking outside
    document.getElementById('alert-modal-bg').addEventListener('click', hideAlertModal);

    // Start polling for alerts
    console.log('Starting alert polling system...');
    setInterval(fetchActiveAlerts, ALERT_POLL_INTERVAL);

    // Initial fetch with slight delay to ensure DOM is fully loaded
    setTimeout(fetchActiveAlerts, 1000);

    // Log the current state of dismissed alerts (for debugging)
    console.log('Alert system initialized with 5-minute dismissal period');
    console.log('Alerts will not reappear for 5 minutes after being dismissed');

    // Periodically log the state of dismissed alerts
    setInterval(() => {
        const count = Object.keys(dismissedAlerts).length;
        if (count > 0) {
            console.log(`Currently tracking ${count} dismissed alerts:`);

            // Show when each alert will reappear
            Object.keys(dismissedAlerts).forEach(key => {
                const dismissedTime = dismissedAlerts[key];
                const reappearTime = new Date(dismissedTime + ALERT_RESHOW_DELAY);
                const timeRemaining = Math.max(0, Math.floor((dismissedTime + ALERT_RESHOW_DELAY - Date.now()) / 1000));
                const minutes = Math.floor(timeRemaining / 60);
                const seconds = timeRemaining % 60;

                console.log(`  - ${key}: will reappear at ${reappearTime.toLocaleTimeString()} (in ${minutes}m ${seconds}s)`);
            });
        }
    }, 60000); // Log every minute if there are dismissed alerts
});

// Add pulse animation to alert icon when showing critical alerts
function pulseAlertIcon() {
    const icon = document.getElementById('alert-modal-icon');
    icon.classList.add('animate-pulse');
}
</script>
</body>
</html>

