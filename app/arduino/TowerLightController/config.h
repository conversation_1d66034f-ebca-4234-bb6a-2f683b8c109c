#ifndef CONFIG_H
#define CONFIG_H

// --- WiFi Credentials ---
#define WIFI_SSID "AlensteinM"               // Your WiFi SSID
#define WIFI_PASSWORD "_4067m3@/X"         // Your WiFi Password
#define WIFI_RETRY_DELAY 5000               // Milliseconds to wait between WiFi connection retries
#define WIFI_MAX_RETRIES 20                 // Maximum number of WiFi connection retries
#define WIFI_CONNECT_TIMEOUT_MS 20000       // Timeout for initial WiFi connection attempt (ms), 0 for no timeout

// --- Server Configuration ---
#define SERVER_HOST "*************"       // Your Django server IP/hostname
#define SERVER_PORT 8000                    // Your Django server port
#define USE_HTTPS false                     // Set to true if using HTTPS for server communication

// API Endpoints (relative to SERVER_HOST:SERVER_PORT)
#define API_BASE_PATH "/api"                                // Base path for all API calls
#define SENSOR_DATA_ENDPOINT_PATH "/monitoring/sensor-data/"  // Endpoint for sending sensor data
#define STATUS_UPDATE_ENDPOINT_PATH "/tower-status-update/" // Endpoint for sending status updates
#define COMMAND_POLL_ENDPOINT_PATH "/tower-commands/"       // Endpoint for polling commands

// --- Device Configuration ---
#define TOWER_ID 1                          // Unique Tower ID
#define FIRMWARE_VERSION "1.0.4"            // Firmware version

// --- Pin Definitions ---
// Analog Inputs (ADC1)
#define VOLTAGE_PIN 22      // ESP32 pin for voltage sensor
#define CURRENT_PIN 34      // ESP32 pin for current sensor
#define LDR_PIN 33          // ESP32 pin for LDR sensor

// Digital I/O
#define RELAY_PIN 21        // ESP32 pin for relay control
#define STATUS_LED_PIN 2    // ESP32 pin for status LED (e.g., built-in LED)

// --- ADC General Configuration ---
#define ADC_MAX 4095.0f              // Max value for 12-bit ADC (ESP32)
#define ADC_VREF 3.3f                // ESP32 ADC reference voltage (V)

// --- Voltage Sensor Configuration ---
#define VOLTAGE_R1 30000.0f                 // R1 for voltage divider
#define VOLTAGE_R2 7500.0f                  // R2 for voltage divider
#define VOLTAGE_DIVIDER_RATIO ( (VOLTAGE_R1 + VOLTAGE_R2) / VOLTAGE_R2 ) 
#define VOLTAGE_SENSE_MAX_EXPECTED 12.3f    // Max valid voltage reading
#define VOLTAGE_SENSE_NOISE_THRESHOLD 0.1f  // Voltage noise threshold

// --- Current Sensor Configuration ---
#define CURRENT_SCALE_FACTOR 0.185f         // For ACS712 5A version
#define CURRENT_NUM_SAMPLES 1000            // Number of samples for current averaging
#define CURRENT_ZERO_POINT 1.235f           // Measured no-load Vout for current sensor
#define CURRENT_MAX_EXPECTED 1.5f           // Upper limit for real current
#define CURRENT_NOISE_THRESHOLD 0.05f       // Ignore small current fluctuations

// --- LDR & Lux Calibration ---
#define LDR_DIVIDER_RESISTOR 10000.0f 
#define LDR_RAW_ADC_MIN 18            
#define LDR_RAW_ADC_MAX 4095          
#define LUX_EST_M 18061.92f           
#define LUX_EST_C -3.41f              
#define MAX_EXPECTED_LUX 2000.0f      

// --- Control Thresholds ---
#define VOLTAGE_LOW_THRESHOLD 10.0f   
#define VOLTAGE_HIGH_THRESHOLD 12.5f  
#define CURRENT_LOW_THRESHOLD 0.02f   
#define CURRENT_HIGH_THRESHOLD 2.50f   
#define POWER_LOW_THRESHOLD 2.0f      
#define LIGHT_PERCENT_DARK_THRESHOLD 20 

// --- Timing Configuration (milliseconds) ---
#define READ_INTERVAL_MS 200
#define DATA_SEND_INTERVAL_MS 30000
#define STATUS_UPDATE_INTERVAL_MS 60000
#define DISPLAY_UPDATE_INTERVAL_MS 10000
#define COMMAND_POLL_INTERVAL_MS 5000       // Poll for commands every 5 seconds

// --- JSON Buffer Sizes (bytes) ---
#define JSON_SENSOR_DATA_SIZE 512    
#define JSON_STATUS_UPDATE_SIZE 256  

// --- Error Retry Configuration ---
#define MAX_HTTP_RETRIES 3           
#define HTTP_RETRY_DELAY_MS 2000     

// --- Manual Control Timeout ---
#define MANUAL_CONTROL_TIMEOUT_MS 300000 

// --- Time Configuration (NTP and Night Hours) ---
#define NTP_SERVER_1 "pool.ntp.org"
#define NTP_SERVER_2 "time.google.com"
#define TIMEZONE_OFFSET_GMT_HOURS 2      
#define TIMEZONE_DAYLIGHT_OFFSET_SEC 0   

#define NIGHT_HOUR_START 18 
#define NIGHT_MINUTE_START 0
#define NIGHT_HOUR_END 6    
#define NIGHT_MINUTE_END 0

#endif // CONFIG_H
