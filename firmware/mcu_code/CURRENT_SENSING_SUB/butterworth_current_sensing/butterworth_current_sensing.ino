#include <Arduino.h>

// --- Pin Definitions ---
#define CURRENT_PIN 34      // ADC1_CH7

// --- Sensor Calibration & Constants ---
#define ACS712_SENSITIVITY 0.100 // V/A (100mV/A for 20A version) 
#define ACS712_ZERO_VOLTAGE 1.65 // Approx VCC/2 for 3.3V supply
#define ADC_MAX 4095.0
#define ADC_VREF 5

// --- Butterworth Filter Configuration ---
// Assuming 5 Hz sampling rate (READ_INTERVAL_MS = 200ms) and 0.2 Hz cutoff
const unsigned long READ_INTERVAL_MS = 200; // How often to read
const double BUTTER_B0 = 0.013368;
const double BUTTER_B1 = 0.026736;
const double BUTTER_B2 = 0.013368;
const double BUTTER_A1 = -1.647198;
const double BUTTER_A2 = 0.700802;

// Filter state variables for Current Sensor Voltage
double curr_v_x_hist[2] = {0.0, 0.0};
double curr_v_y_hist[2] = {0.0, 0.0};
bool curr_v_filterInitialized = false;

// --- Global Variables ---
unsigned long lastReadTime = 0;

// --- Function Prototypes ---
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized);
double readFilteredCurrent();

// ========================== SETUP ==========================
void setup() {
  Serial.begin(115200);
  while (!Serial); // Wait for Serial connection
  Serial.println("\n--- Subsystem Test: DC Current Measurement (ACS712) ---");
  Serial.print("Reading from ADC pin: "); Serial.println(CURRENT_PIN);
  Serial.print("Zero Current Voltage (CALIBRATE THIS!): "); Serial.println(ACS712_ZERO_VOLTAGE, 3);
  Serial.print("Sensitivity (V/A): "); Serial.println(ACS712_SENSITIVITY, 3);
  Serial.print("Read Interval (ms): "); Serial.println(READ_INTERVAL_MS);
  Serial.println("Connect sensor with NO current flowing initially to observe Zero Voltage.");
  Serial.println("-----------------------------------------------------------------------");

  pinMode(CURRENT_PIN, INPUT);

  // Initialize filter by reading once
  readFilteredCurrent();
  lastReadTime = millis();
}

// ========================== LOOP ==========================
void loop() {
  unsigned long currentTime = millis();

  if (currentTime - lastReadTime >= READ_INTERVAL_MS) {
    lastReadTime = currentTime;

    // Read raw ADC value first for comparison
    int rawAdc = analogRead(CURRENT_PIN);
    double rawSensorVoltage = (rawAdc / ADC_MAX) * ADC_VREF;

    // Read the filtered current
    double filteredCurrent = readFilteredCurrent();

    // Also get the filtered sensor voltage (calculated inside readFilteredCurrent)
     double filteredSensorVoltage = curr_v_y_hist[0]; // The latest filtered output

    // Print results
    Serial.print("Raw ADC: "); Serial.print(rawAdc);
    Serial.print("\tRaw Sens V: "); Serial.print(rawSensorVoltage, 3);
    Serial.print("\tFilt Sens V: "); Serial.print(filteredSensorVoltage, 3);
    Serial.print("\tFiltered Current: "); Serial.print(filteredCurrent, 3);
    Serial.println(" A");
  }
  // No delay needed, timing is handled by millis()
}

// ========================== HELPER FUNCTIONS ==========================

/**
 * @brief Applies a 2nd order Butterworth low-pass filter.
 */
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized) {
  double xn = rawValue;
  if (!initialized) {
      x_hist[0] = xn; x_hist[1] = xn;
      y_hist[0] = xn; y_hist[1] = xn;
      initialized = true;
  }
  double yn = BUTTER_B0 * xn + BUTTER_B1 * x_hist[0] + BUTTER_B2 * x_hist[1] - BUTTER_A1 * y_hist[0] - BUTTER_A2 * y_hist[1];
  x_hist[1] = x_hist[0]; x_hist[0] = xn;
  y_hist[1] = y_hist[0]; y_hist[0] = yn;
  return yn;
}

/**
 * @brief Reads the ACS712 sensor, applies filtering to the sensor voltage, and calculates current.
 * @return Filtered current reading in Amperes.
 */
double readFilteredCurrent() {
  int rawAdc = analogRead(CURRENT_PIN);
  double sensorVoltage = (rawAdc / ADC_MAX) * ADC_VREF;

  // Apply filter to the sensor voltage BEFORE calculating current
  double filteredSensorVoltage = applyButterworth(sensorVoltage, curr_v_x_hist, curr_v_y_hist, curr_v_filterInitialized);

  // Calculate current from the filtered sensor voltage
  double current = (filteredSensorVoltage - ACS712_ZERO_VOLTAGE) / ACS712_SENSITIVITY;

  // Basic noise filter around zero
  return abs(current) > 0.05 ? current : 0.0; // Adjust threshold as needed
}
