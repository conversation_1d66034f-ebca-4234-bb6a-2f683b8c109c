{% extends 'base.html' %}

{% block title %}Clear Browser Alerts{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto bg-surface rounded-lg shadow-lg p-6">
        <h1 class="text-2xl font-bold mb-6 text-center">Clear Browser Alerts</h1>
        
        <div class="mb-6 p-4 bg-surface-light rounded-lg">
            <p class="mb-4">
                This page helps you clear any lingering alert popups that might be stored in your browser's localStorage.
                This is useful after cleaning the database to ensure no old alerts reappear.
            </p>
            <p>
                Click the button below to clear all dismissed alerts from your browser's localStorage.
            </p>
        </div>
        
        <div id="status-container" class="mb-6 p-4 rounded-lg hidden">
            <p id="status-message" class="text-center"></p>
        </div>
        
        <div class="flex justify-center">
            <button id="clear-alerts-btn" class="px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors duration-200 font-medium shadow-lg hover:shadow-xl">
                Clear All Alert Data
            </button>
        </div>
        
        <div class="mt-8 border-t border-gray-700 pt-4">
            <h2 class="text-lg font-semibold mb-2">Current Alert Data</h2>
            <div class="bg-gray-800 p-4 rounded-lg">
                <pre id="current-alerts" class="text-sm text-gray-300 whitespace-pre-wrap">Loading...</pre>
            </div>
        </div>
        
        <div class="mt-6 text-center">
            <a href="{% url 'monitoring:dashboard' %}" class="text-primary hover:text-primary-light transition-colors">
                Return to Dashboard
            </a>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const clearAlertsBtn = document.getElementById('clear-alerts-btn');
    const statusContainer = document.getElementById('status-container');
    const statusMessage = document.getElementById('status-message');
    const currentAlerts = document.getElementById('current-alerts');
    
    // Display current alert data
    function displayCurrentAlerts() {
        try {
            const dismissedAlerts = localStorage.getItem('dismissedAlerts');
            if (dismissedAlerts) {
                const alertsObj = JSON.parse(dismissedAlerts);
                const alertCount = Object.keys(alertsObj).length;
                
                if (alertCount > 0) {
                    // Format the JSON for better readability
                    const formattedJson = JSON.stringify(alertsObj, null, 2);
                    currentAlerts.textContent = `Found ${alertCount} dismissed alerts:\n\n${formattedJson}`;
                } else {
                    currentAlerts.textContent = 'No dismissed alerts found in localStorage.';
                }
            } else {
                currentAlerts.textContent = 'No dismissed alerts found in localStorage.';
            }
        } catch (e) {
            currentAlerts.textContent = `Error reading localStorage: ${e.message}`;
        }
    }
    
    // Clear all alert data
    clearAlertsBtn.addEventListener('click', function() {
        try {
            // Check if there are any alerts to clear
            const dismissedAlerts = localStorage.getItem('dismissedAlerts');
            
            if (dismissedAlerts) {
                const alertsObj = JSON.parse(dismissedAlerts);
                const alertCount = Object.keys(alertsObj).length;
                
                // Remove the alerts from localStorage
                localStorage.removeItem('dismissedAlerts');
                
                // Show success message
                statusContainer.classList.remove('bg-red-900', 'hidden');
                statusContainer.classList.add('bg-green-900');
                statusMessage.textContent = `Successfully cleared ${alertCount} dismissed alerts from localStorage.`;
            } else {
                // No alerts to clear
                statusContainer.classList.remove('bg-red-900', 'hidden');
                statusContainer.classList.add('bg-green-900');
                statusMessage.textContent = 'No dismissed alerts found in localStorage.';
            }
            
            // Update the display
            displayCurrentAlerts();
            
        } catch (e) {
            // Show error message
            statusContainer.classList.remove('bg-green-900', 'hidden');
            statusContainer.classList.add('bg-red-900');
            statusMessage.textContent = `Error clearing alerts: ${e.message}`;
        }
    });
    
    // Initial display
    displayCurrentAlerts();
});
</script>
{% endblock %}
