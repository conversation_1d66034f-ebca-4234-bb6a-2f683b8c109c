from rest_framework import serializers
from monitoring.models import <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Technician, MaintenanceSchedule, SensorData

class TowerLightSerializer(serializers.ModelSerializer):
    class Meta:
        model = TowerLight
        fields = ['id', 'name', 'status', 'latitude', 'longitude']
        read_only_fields = ['id']

    def validate_status(self, value):
        valid_statuses = ['active', 'inactive', 'faulty', 'maintenance']
        if value not in valid_statuses:
            raise serializers.ValidationError(f"Status must be one of: {', '.join(valid_statuses)}")
        return value

class FaultLogSerializer(serializers.ModelSerializer):
    tower_name = serializers.CharField(source='tower.name', read_only=True)
    resolved_by_name = serializers.CharField(source='resolved_by.get_full_name', read_only=True)

    class Meta:
        model = FaultLog
        fields = '__all__'

class TechnicianSerializer(serializers.ModelSerializer):
    user_full_name = serializers.Char<PERSON>ield(source='user.get_full_name', read_only=True)

    class Meta:
        model = Technician
        fields = '__all__'

class MaintenanceScheduleSerializer(serializers.ModelSerializer):
    tower_name = serializers.CharField(source='tower.name', read_only=True)
    technician_name = serializers.CharField(source='technician.user.get_full_name', read_only=True)

    class Meta:
        model = MaintenanceSchedule
        fields = '__all__'

class SensorDataSerializer(serializers.ModelSerializer):
    tower_name = serializers.CharField(source='tower.name', read_only=True)

    class Meta:
        model = SensorData
        fields = ['id', 'tower', 'tower_name', 'timestamp', 'sensor_value', 'light_level',
                 'faulty_lights', 'maintenance_lights', 'efficiency', 'voltage', 'current', 'power']
        read_only_fields = ['id', 'timestamp']
