#include <Wire.h>
#include <LiquidCrystal_I2C.h>

#define LDR_PIN 34  // Analog pin for LDR
#define GREEN_LED 16 // Normal
#define YELLOW_LED 17 // Warning
#define RED_LED 2 // Alert

// Initialize LCD (0x27 or 0x3F for I2C address, 16 columns x 2 rows)
LiquidCrystal_I2C lcd(0x27, 16, 2);

void setup() {
    Serial.begin(115200);
    
    pinMode(GREEN_LED, OUTPUT);
    pinMode(YELLOW_LED, OUTPUT);
    pinMode(RED_LED, OUTPUT);
    
    lcd.init();
    lcd.backlight();
    lcd.setCursor(0, 0);
    lcd.print("LDR Sensing CKT");
}

void loop() {
    int ldrValue = analogRead(LDR_PIN); // Read LDR value (0-4095)
    Serial.println(ldrValue);
    
    // Convert LDR value to percentage
    int lightPercent = map(ldrValue, 0, 4095, 100, 0);
    
    // Determine LED indicators
    if (lightPercent > 70) {
        digitalWrite(GRE<PERSON>_LED, HIGH);
        digitalWrite(YELLOW_LED, LOW);
        digitalWrite(RED_LED, LOW);
    } else if (lightPercent > 40) {
        digitalWrite(GREEN_LED, LOW);
        digitalWrite(YELLOW_LED, HIGH);
        digitalWrite(RED_LED, LOW);
    } else {
        digitalWrite(GREEN_LED, LOW);
        digitalWrite(YELLOW_LED, LOW);
        digitalWrite(RED_LED, HIGH);
    }

    // Display values on Serial Monitor
    Serial.print("LDR Value: ");
    Serial.print(ldrValue);
    Serial.print(" | Light Intensity: ");
    Serial.print(lightPercent);
    Serial.println("%");

    // Display values on LCD
    lcd.setCursor(0, 2);
  
    lcd.print("Light:   ");
    lcd.print(lightPercent);
    lcd.print("%   ");  // Space to clear old values

    delay(300); // Update every second
}
