#include <Arduino.h> // Include base Arduino library

// --- Configuration ---
const int PIN_LIGHT_SENSOR = 33; // GPIO33 (ADC1_CH5)
const bool HIGHER_READING_IS_BRIGHTER = false; // <<< ADJUST BASED ON YOUR CIRCUIT! (False means lower ADC = brighter)
const int RAW_ADC_MIN = 0;    // Calibration: Raw value in darkest condition (Theoretically 0, practically higher in low light)
const int RAW_ADC_MAX = 4095; // Calibration: Raw value in brightest condition (Theoretically 4095, practically lower in bright light)
const unsigned long READ_INTERVAL_MS = 200; // How often to read (milliseconds) - Kept at 200ms for faster updates

// --- Relay Configuration ---
const int PIN_RELAY = 21; // Digital pin connected to the relay IN terminal

// --- Relay Switching Thresholds (Estimated Lux) ---
// Adjust these values based on your desired ON/OFF light levels.
// RELAY_OFF_LUX_THRESHOLD MUST be greater than <PERSON><PERSON><PERSON><PERSON>_ON_LUX_THRESHOLD for hysteresis.
// Observe the "Lux (Est)" output to help you set these thresholds.
const double RELAY_ON_LUX_THRESHOLD = 20.0;  // Turn relay ON when light falls BELOW this Lux level (e.g., getting dark)
const double RELAY_OFF_LUX_THRESHOLD = 50.0; // Turn relay OFF when light rises ABOVE this Lux level (e.g., getting bright)

// --- Butterworth Filter Configuration ---
// Renamed coefficients to avoid conflict with core library definitions (B0, B1, etc.)
// Recalculated for: fs = 1.0 / (READ_INTERVAL_MS / 1000.0) Hz  (Approx 5 Hz here)
//                 fc = 0.2 Hz (Cutoff Frequency - Increased smoothing compared to previous effective cutoff)
// If you change READ_INTERVAL_MS or need different filtering, RECALCULATE these!
const double BUTTER_B0 = 0.013368; // New coefficient for fc = 0.2 Hz @ fs = 5 Hz
const double BUTTER_B1 = 0.026736; // New coefficient for fc = 0.2 Hz @ fs = 5 Hz
const double BUTTER_B2 = 0.013368; // New coefficient for fc = 0.2 Hz @ fs = 5 Hz
const double BUTTER_A1 = -1.647198; // New coefficient for fc = 0.2 Hz @ fs = 5 Hz
const double BUTTER_A2 = 0.700802;  // New coefficient for fc = 0.2 Hz @ fs = 5 Hz // Note: a0 is implicitly 1


// Filter state variables (history)
double x_hist[2] = {0.0, 0.0}; // Previous 2 input values x[n-1], x[n-2]
double y_hist[2] = {0.0, 0.0}; // Previous 2 output values y[n-1], y[n-2]
bool filterInitialized = false;

// --- Lux Conversion Parameters (Empirical Estimation) ---
// These values are derived assuming a roughly inverse relationship between raw ADC and Lux,
// calibrated based on your observed points:
// Raw Value ~4095 (darkest) corresponds to approx 1 Lux.
// Raw Value ~18   (brightest) corresponds to approx 1000 Lux.
// The formula used is approximately: Estimated Lux = M / Filtered_Value + C
// *** This is an ESTIMATION based on limited data. For accurate Lux, you need:
//     1. A specific LDR datasheet.
//     2. The exact value of the fixed resistor in your circuit.
//     3. Calibration with a known light meter across the full range. ***
const double LUX_EST_M = 18061.92; // Derived slope for 1/Value mapping
const double LUX_EST_C = -3.41;   // Derived intercept for 1/Value mapping

// Estimated Lux values used for deriving the formula (for reference)
const double LUX_REF_AT_DARKEST_RAW = 1.0;  // Reference Lux at raw 4095
const double LUX_REF_AT_BRIGHTEST_RAW = 1000.0; // Reference Lux at raw 18


// --- State Variable ---
bool relayIsOn = false; // Keep track of the current relay state (false = OFF, true = ON)

// --- Function Prototypes ---
double applyButterworth(int rawValue);
double getLightLux(double filteredValue);
String getQualitativeLightLevel(double luxValue);


// ==========================
// SETUP FUNCTION
// ==========================
void setup() {
  Serial.begin(115200);
  while (!Serial);
  Serial.println("\n--- ESP32 LDR Light Sensor with Butterworth Filter and Relay Control ---");
  Serial.print("Reading from ADC pin: "); Serial.println(PIN_LIGHT_SENSOR);
  Serial.print("Controlling Relay on pin: "); Serial.println(PIN_RELAY);
  Serial.print("Higher ADC reading means: "); Serial.println(HIGHER_READING_IS_BRIGHTER ? "Brighter" : "Darker");
  Serial.print("Read Interval (ms): "); Serial.println(READ_INTERVAL_MS);

  // Calculate and print the effective absolute cutoff frequency
  double sampling_rate = 1000.0 / READ_INTERVAL_MS;
  // The filter coefficients were calculated for fc = 0.2 Hz at this fs = 5 Hz.
  Serial.print("Filter Cutoff Freq (Hz): "); Serial.println(0.2);

  Serial.println("-----------------------------------------------------");
  Serial.println("Outputting ESTIMATED Lux values (wider dynamic range).");
  Serial.print("Estimation formula based on: Raw "); Serial.print(RAW_ADC_MAX); Serial.print(" -> approx "); Serial.print(LUX_REF_AT_DARKEST_RAW); Serial.print(" Lux");
  Serial.print(", Raw ~18 -> approx "); Serial.print(LUX_REF_AT_BRIGHTEST_RAW); Serial.println(" Lux");
   Serial.println("Output Lux range is now less strictly clamped, allowing for higher values in bright light.");
  Serial.println("-----------------------------------------------------");

  // Configure the relay pin as an output
  pinMode(PIN_RELAY, OUTPUT);

  // *** IMPORTANT: Check your relay module's documentation! ***
  // Many relay modules are "active LOW", meaning writing LOW to the IN pin
  // activates the relay, and HIGH deactivates it. If yours is active LOW,
  // swap HIGH and LOW in the digitalWrite calls below and in the loop().
  Serial.println("Relay control assumes HIGH activates the relay. Adjust digitalWrite if your relay is active LOW.");

  // Set initial relay state based on the first light reading
  int initialRaw = analogRead(PIN_LIGHT_SENSOR);
  // Apply filter logic once to get an initial filtered value
  double initialFiltered = applyButterworth(initialRaw);
  double initialLux = getLightLux(initialFiltered);

  Serial.print("Initial Estimated Lux: "); Serial.println(initialLux, 1);

  if (initialLux < RELAY_ON_LUX_THRESHOLD) {
      digitalWrite(PIN_RELAY, HIGH); // Assuming HIGH turns the relay ON
      relayIsOn = true;
      Serial.println("Initial light is low: Relay is ON.");
  } else {
      digitalWrite(PIN_RELAY, LOW); // Assuming LOW turns the relay OFF
      relayIsOn = false;
      Serial.println("Initial light is high: Relay is OFF.");
  }
  Serial.println("-----------------------------------------------------");
}

// ==========================
// MAIN LOOP FUNCTION
// ==========================
void loop() {
  // 1. Read the raw analog value
  int rawValue = analogRead(PIN_LIGHT_SENSOR);

  // 2. Apply the Butterworth filter
  double filteredValue = applyButterworth(rawValue);

  // 3. Convert filtered value to an approximate Lux value
  double lightLux = getLightLux(filteredValue);

  // 4. Get a qualitative description based on the LUX value
  String lightDescription = getQualitativeLightLevel(lightLux);

  // 5. Print the results
  Serial.print("Raw: "); Serial.print(rawValue);
  Serial.print("\tFiltered: "); Serial.print((int)filteredValue); // Print filtered value as int
  Serial.print("\tLux (Est): "); Serial.print(lightLux, 1); // Print Estimated Lux with 1 decimal place
  Serial.print("\tLevel: "); Serial.println(lightDescription);

  // --- 6. Control the Relay based on Estimated Lux (with Hysteresis) ---
  if (!relayIsOn && lightLux < RELAY_ON_LUX_THRESHOLD) {
      // Current state is OFF, and light level dropped below the ON threshold
      digitalWrite(PIN_RELAY, HIGH); // Assuming HIGH turns the relay ON
      relayIsOn = true;
      Serial.print("Light fell below "); Serial.print(RELAY_ON_LUX_THRESHOLD, 1); Serial.println(" Lux: Turning Relay ON.");
  } else if (relayIsOn && lightLux > RELAY_OFF_LUX_THRESHOLD) {
      // Current state is ON, and light level rose above the OFF threshold
      digitalWrite(PIN_RELAY, LOW); // Assuming LOW turns the relay OFF
      relayIsOn = false;
      Serial.print("Light rose above "); Serial.print(RELAY_OFF_LUX_THRESHOLD, 1); Serial.println(" Lux: Turning Relay OFF.");
  }
  // --- End Relay Control ---


  // Wait for the defined interval
  delay(READ_INTERVAL_MS);
}

// ==========================
// HELPER FUNCTIONS
// ==========================

/**
 * @brief Applies a 2nd order Butterworth low-pass filter.
 * Uses renamed coefficients (BUTTER_*).
 * @param rawValue The current raw input value (x[n]).
 * @return The filtered output value (y[n]).
 */
double applyButterworth(int rawValue) {
  double xn = (double)rawValue; // Current input x[n]

  if (!filterInitialized) {
      x_hist[0] = xn;
      x_hist[1] = xn;
      y_hist[0] = xn;
      y_hist[1] = xn;
      filterInitialized = true;
  }

  // The difference equation using renamed coefficients:
  // y[n] = BUTTER_B0*x[n] + BUTTER_B1*x[n-1] + BUTTER_B2*x[n-2] - BUTTER_A1*y[n-1] - BUTTER_A2*y[n-2]
  double yn = BUTTER_B0 * xn + BUTTER_B1 * x_hist[0] + BUTTER_B2 * x_hist[1] - BUTTER_A1 * y_hist[0] - BUTTER_A2 * y_hist[1];

  // Update history for next iteration:
  x_hist[1] = x_hist[0];
  x_hist[0] = xn;
  y_hist[1] = y_hist[0];
  y_hist[0] = yn;

  return yn;
}

/**
 * @brief Estimates Lux from a raw/filtered ADC value based on empirical calibration.
 * Uses a linear mapping on the inverse of the value (approximating LDR response).
 * Allows for a wider Lux output range.
 * @param value The input value (ideally the filtered ADC reading).
 * @return Estimated Lux value.
 */
double getLightLux(double value) {
  // Ensure input value is within a sensible range observed during calibration,
  // allowing values closer to 0 for brighter light while avoiding division by zero.
  // Clamp the input value between 1.0 and the max ADC value.
  value = constrain(value, 1.0, 4095.0); // Lower clamp at 1.0

  // Apply the linear formula on the inverse of the value to get estimated Lux
  double estimatedLux = LUX_EST_M / value + LUX_EST_C;

  // Ensure Lux is not negative. Removed the upper clamp to allow the Lux value
  // to naturally extend to higher values as the input raw value approaches 0.
  estimatedLux = max(0.0, estimatedLux); // Lux cannot be negative

  // Optional: Keep a lower bound on Lux display for robustness in very dark conditions
  // For example, clamp minimum to 0.5 Lux, assuming below that is effectively dark.
   estimatedLux = max(0.5, estimatedLux);


  // If HIGHER_READING_IS_BRIGHTER is true, we would need a different mapping.
  // The current mapping assumes false (lower ADC = brighter).

  return estimatedLux;
}


/**
 * @brief Provides a qualitative description based on an estimated Lux value.\
 * @param luxValue The estimated Lux value.
 * @return A String describing the light level.
 * @note *** ADJUST THESE LUX THRESHOLDS based on observing the ESTIMATED LUX values! ***
 */
String getQualitativeLightLevel(double luxValue) {
  // These thresholds are SUGGESTIONS based on the estimated Lux scale.
  // Since the Lux range is now wider, you WILL need to adjust these thresholds
  // by observing the "Lux (Est)" output in different conditions
  // and deciding what Lux values correspond to your desired descriptions.
  if (luxValue > 1000.0) return "Very Bright"; // Example thresholds for a potentially wider range
  else if (luxValue > 200.0) return "Bright";
  else if (luxValue > 50.0) return "Moderate";
  else if (luxValue > 10.0) return "Dim";
  else return "Dark";

  // You need to redefine these based on the new estimated Lux scale's behavior.
  // The values above are just starting points for a potentially wider range.
}