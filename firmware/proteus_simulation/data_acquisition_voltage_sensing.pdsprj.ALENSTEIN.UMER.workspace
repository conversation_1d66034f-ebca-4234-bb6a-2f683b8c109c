<?xml version='1.0' encoding='UTF-8' standalone='yes'?>
<WORKSPACE>
 <FRAME activewindow="0">
  <PLACEMENT>2c00000002000000030000000083ffff0083ffffffffffffffffffff0d000000000000007606000004040000</PLACEMENT>
  <WINDOW type="default" module="ISIS">
   <editor metric="0" gridmajor="2540000" mode="00000000" zoom="-1" scale="89" flipped="0" gridminor="254000" gridmode="32" snapidx="1" snap="127000" xcursor="0" orgmode="0" snaprange="142696">
    <world y1="-12700000" x1="-19050000" y2="12700000" x2="19050000"/>
    <centre x="0" y="14269"/>
    <origin x="0" y="0"/>
   </editor>
  </WINDOW>
 </FRAME>
 <MODULE name="ARES">
  <editor metric="1" gridmajor="0" mode="00000000" zoom="10" scale="10" flipped="0" gridminor="0" gridmode="32" snapidx="3" snap="0" xcursor="0" orgmode="0" snaprange="0">
   <world y1="-12700000" x1="-15240000" y2="12700000" x2="15240000"/>
   <centre x="0" y="0"/>
   <origin x="0" y="0"/>
  </editor>
 </MODULE>
 <MODULE name="ISIS">
  <editor metric="0" gridmajor="2540000" mode="00000000" zoom="-1" scale="89" flipped="0" gridminor="254000" gridmode="32" snapidx="1" snap="127000" xcursor="0" orgmode="0" snaprange="142696">
   <world y1="-12700000" x1="-19050000" y2="12700000" x2="19050000"/>
   <centre x="0" y="14269"/>
   <origin x="0" y="0"/>
  </editor>
 </MODULE>
 <MODULE name="VSMDEBUG">
  <PWI>
   <POPUP w="800" x="48" flags="00000002" y="160" h="200" pid="0" iid="-1">
    <PROPERTIES>
     <ITEM name="Message Column Width">661</ITEM>
     <ITEM name="ShowGrid">No</ITEM>
     <ITEM name="Source Column Width">100</ITEM>
     <ITEM name="Version">100</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="350" x="68" flags="00000032" y="180" h="200" pid="1" iid="-1">
    <PROPERTIES>
     <ITEM name="Address Column Width">82</ITEM>
     <ITEM name="AutoResize">No</ITEM>
     <ITEM name="Gridlines">Yes</ITEM>
     <ITEM name="Name Column Width">82</ITEM>
     <ITEM name="ShowAddresses">Yes</ITEM>
     <ITEM name="ShowPreviousValues">No</ITEM>
     <ITEM name="ShowTypes">No</ITEM>
     <ITEM name="ShowWatchPoint">Yes</ITEM>
     <ITEM name="TriggerMode">0</ITEM>
     <ITEM name="Value Column Width">82</ITEM>
     <ITEM name="Version">100</ITEM>
     <ITEM name="Watch Expression Column Width">82</ITEM>
     <ITEM name="nItems">0</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="30" x="88" flags="00000008" y="200" h="16" pid="3" iid="10">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="8" x="108" flags="0000000a" y="220" h="1" pid="10" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="8" x="128" flags="0000000a" y="240" h="32" pid="6" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="16" x="148" flags="0000000a" y="260" h="32" pid="7" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="16" x="168" flags="00000000" y="280" h="32" pid="14" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000000</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="16" x="188" flags="0000000a" y="300" h="32" pid="4" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000100</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="16" x="208" flags="0000000a" y="320" h="8" pid="13" iid="10">
    <PROPERTIES>
     <ITEM name="DataType">2</ITEM>
     <ITEM name="DispChars">Yes</ITEM>
     <ITEM name="DispFormat">2</ITEM>
     <ITEM name="ShowChanges">Yes</ITEM>
     <ITEM name="ShowToolTips">Yes</ITEM>
     <ITEM name="TopLineAddress">00000020</ITEM>
    </PROPERTIES>
   </POPUP>
   <POPUP w="600" x="48" flags="0000000b" y="160" h="400" pid="1" iid="10">
    <PROPERTIES/>
   </POPUP>
   <POPUP w="300" x="68" flags="0000000b" y="180" h="300" pid="2" iid="10">
    <PROPERTIES>
     <ITEM name="AutoResize">No</ITEM>
     <ITEM name="Gridlines">Yes</ITEM>
     <ITEM name="ShowAddresses">Yes</ITEM>
     <ITEM name="ShowGlobals">Yes</ITEM>
     <ITEM name="ShowPreviousValues">No</ITEM>
     <ITEM name="ShowTypes">No</ITEM>
     <ITEM name="ShowWatchPoint">No</ITEM>
     <ITEM name="TriggerMode">0</ITEM>
     <ITEM name="Version">100</ITEM>
     <ITEM name="nItems">0</ITEM>
    </PROPERTIES>
   </POPUP>
  </PWI>
 </MODULE>
</WORKSPACE>
