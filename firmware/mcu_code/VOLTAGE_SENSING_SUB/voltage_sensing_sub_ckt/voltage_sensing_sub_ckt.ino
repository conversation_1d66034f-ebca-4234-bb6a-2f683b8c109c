#include <Wire.h>
#include <LiquidCrystal_I2C.h>

#define VOLTAGE_SENSOR_PIN 34  // ADC input

#define RED_LED 2              // Over-voltage & under-voltage alert
#define YELLOW_LED 17          // Voltage warning
#define GREEN_LED 16           // Normal voltage

LiquidCrystal_I2C lcd(0x27, 20, 4);  // 20x4 LCD, will use first 16 chars

const float VREF = 3.3;              // ESP32 ADC Reference Voltage
const int ADC_MAX = 4095;            // ESP32 ADC Resolution (12-bit)

// ⚡ Updated voltage divider ratio (R1 = 1MΩ, R2 = 13kΩ)
const float VOLTAGE_DIVIDER_RATIO = (1000000.0 + 13000.0) / 13000.0;  

const float SMOOTHING_FACTOR = 0.1;  // Low-pass filter for stability

float smoothedVoltage = 0;  // Stores filtered voltage

void setup() {
  Serial.begin(115200);
  lcd.init();
  lcd.backlight();

  pinMode(RED_LED, OUTPUT);
  pinMode(Y<PERSON>LOW_LED, OUTPUT);
  pinMode(GREEN_LED, OUTPUT);

  lcd.setCursor(0, 0);
  lcd.print("Voltage Monitor");
}

void loop() {
  // Read raw sensor value and convert to AC voltage
  int rawADC = analogRead(VOLTAGE_SENSOR_PIN);
  float voltage = (rawADC * VREF / ADC_MAX) * VOLTAGE_DIVIDER_RATIO;

  // Apply smoothing filter
  smoothedVoltage = (SMOOTHING_FACTOR * voltage) + ((1 - SMOOTHING_FACTOR) * smoothedVoltage);

  // Voltage thresholds
  lcd.setCursor(0, 1);
  lcd.print("Voltage: ");
  lcd.print(smoothedVoltage, 1);
  lcd.print("V  ");

  lcd.setCursor(0, 2);

  if (smoothedVoltage < 180) {
    digitalWrite(RED_LED, HIGH);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("UNDER VOLTAGE! ");
  }
  else if (smoothedVoltage >= 180 && smoothedVoltage < 200) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, HIGH);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("LOW VOLT WARN! ");
  }
  else if (smoothedVoltage >= 200 && smoothedVoltage <= 240) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, HIGH);
    lcd.print("NORMAL VOLTAGE ");
  }
  else if (smoothedVoltage > 240 && smoothedVoltage <= 250) {
    digitalWrite(RED_LED, LOW);
    digitalWrite(YELLOW_LED, HIGH);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("HIGH VOLT WARN!");
  }
  else {
    digitalWrite(RED_LED, HIGH);
    digitalWrite(YELLOW_LED, LOW);
    digitalWrite(GREEN_LED, LOW);
    lcd.print("OVER VOLT ALERT");
  }

  Serial.print("Voltage: ");
  Serial.print(smoothedVoltage);
  Serial.println("V");

  delay(10);
}
