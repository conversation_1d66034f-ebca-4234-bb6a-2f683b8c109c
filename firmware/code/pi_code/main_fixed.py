# main.py
import machine
import network
import urequests as requests # Or just requests if using a version with it built-in
import ujson as json
import utime
import ntptime
import config # Your config.py file

# --- Global Sensor Variables ---
voltage_actual = 0.0
current_actual = 0.0
power_actual = 0.0
light_raw_adc = 0
light_lux_estimated = 0.0
light_percent = 0

# --- Global Status Variables ---
current_status = "inactive"
fault_type = ""
relay_state = False
manual_override = False
manual_override_start_time = 0

# --- Global Fault Flags ---
fault_under_voltage = False
fault_over_voltage = False
fault_low_current = False
fault_high_current = False
fault_low_power = False

# --- Timing variables for data sending ---
last_sensor_update_ms = 0
last_status_update_ms = 0
last_display_update_ms = 0
last_command_poll_ms = 0

# --- Pin Initialization ---
# ADC Pins
adc_voltage = machine.ADC(config.VOLTAGE_PIN_ADC_NUM)
adc_current = machine.ADC(config.CURRENT_PIN_ADC_NUM)
adc_ldr = machine.ADC(config.LDR_PIN_ADC_NUM)

# Digital Pins
relay_pin = machine.Pin(config.RELAY_PIN_NUM, machine.Pin.OUT)
status_led_pin = machine.Pin(config.STATUS_LED_PIN_NUM, machine.Pin.OUT) # "LED" for onboard LED

# --- WiFi Interface ---
wlan = network.WLAN(network.STA_IF)

# --- Helper Functions ---
def blink_led(times, delay_ms):
    """Blinks the status LED."""
    for _ in range(times):
        status_led_pin.on()
        utime.sleep_ms(delay_ms)
        status_led_pin.off()
        if times > 1: # Avoid delay after last blink if only one blink
            utime.sleep_ms(delay_ms)

def map_value(x, in_min, in_max, out_min, out_max):
    """Maps a value from one range to another."""
    if in_max == in_min: # Avoid division by zero
        return out_min
    return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

def get_current_time_tuple():
    """Gets current local time as a tuple, applying timezone offset."""
    try:
        # ntptime.settime() might have been called at startup
        # utime.time() returns seconds since epoch (usually 2000-01-01 for Pico)
        current_utc_secs = utime.time()
        local_time_secs = current_utc_secs + config.TIMEZONE_OFFSET_SECONDS
        return utime.localtime(local_time_secs)
    except Exception as e:
        print(f"Error getting time: {e}")
        # Return a default time or handle error appropriately
        # (year, month, mday, hour, minute, second, weekday, yearday)
        return (2000, 1, 1, 0, 0, 0, 0, 0)


# --- Core Functions ---
def connect_to_wifi():
    """Connects to WiFi."""
    print(f"Connecting to WiFi: '{config.WIFI_SSID}'")
    wlan.active(True)
    wlan.connect(config.WIFI_SSID, config.WIFI_PASSWORD)

    start_time = utime.ticks_ms()
    retries = 0

    print("Connecting...", end="")
    while not wlan.isconnected() and retries < config.WIFI_MAX_RETRIES:
        utime.sleep_ms(config.WIFI_RETRY_DELAY_MS)
        print(".", end="")
        retries += 1
        if config.WIFI_CONNECT_TIMEOUT_MS > 0 and \
                utime.ticks_diff(utime.ticks_ms(), start_time) > config.WIFI_CONNECT_TIMEOUT_MS:
            print("\nConnection attempt timed out.")
            return False
    print()

    if wlan.isconnected():
        print("WiFi Connected!")
        print(f"IP Address: {wlan.ifconfig()[0]}")
        blink_led(3, 200)
        # Synchronize time with NTP server
        try:
            print("Synchronizing time with NTP server...")
            ntptime.settime() # Default server is pool.ntp.org
            print(f"Time synchronized. Current time: {get_current_time_tuple()}")
        except Exception as e:
            print(f"NTP time sync failed: {e}")
        return True
    else:
        print("Failed to connect to WiFi.")
        wlan.active(False) # Turn off WLAN interface if connection failed
        return False

# --- Global variables for detailed logging ---
adc_val_v_raw = 0
adc_voltage_v = 0.0
voltage_divider_ratio = 0.0
avg_adc_current_raw = 0.0
vout_current_sensor = 0.0
ldr_voltage = 0.0
ldr_resistance = 0.0

def update_sensors():
    """Reads sensor values and updates global variables."""
    global voltage_actual, current_actual, power_actual, light_raw_adc, light_lux_estimated, light_percent
    global adc_val_v_raw, adc_voltage_v, voltage_divider_ratio, avg_adc_current_raw, vout_current_sensor, ldr_voltage, ldr_resistance

    # --- Voltage Sensor Reading (Pico ADC is 16-bit: 0-65535) ---
    adc_val_v_raw = adc_voltage.read_u16()
    adc_voltage_v = (adc_val_v_raw / config.ADC_MAX_VALUE) * config.ADC_VREF

    voltage_divider_ratio = (config.VOLTAGE_R1 + config.VOLTAGE_R2) / config.VOLTAGE_R2
    voltage_actual = adc_voltage_v * voltage_divider_ratio

    if voltage_actual < config.VOLTAGE_SENSE_NOISE_THRESHOLD or voltage_actual > config.VOLTAGE_SENSE_MAX_EXPECTED:
        voltage_actual = 0.0

    # --- Current Sensor Reading (ACS712 or similar) ---
    total_adc_current_raw = 0
    for _ in range(config.CURRENT_NUM_SAMPLES):
        total_adc_current_raw += adc_current.read_u16()
        utime.sleep_us(100)

    avg_adc_current_raw = total_adc_current_raw / config.CURRENT_NUM_SAMPLES
    vout_current_sensor = (avg_adc_current_raw / config.ADC_MAX_VALUE) * config.ADC_VREF

    current_actual = (vout_current_sensor - config.CURRENT_ZERO_POINT_VOLTAGE) / config.CURRENT_SCALE_FACTOR

    if abs(current_actual) < config.CURRENT_NOISE_THRESHOLD or current_actual < 0 or current_actual > config.CURRENT_MAX_EXPECTED:
        current_actual = 0.0

    # --- Calculate Power ---
    power_actual = voltage_actual * current_actual
    if power_actual < 0:
        power_actual = 0.0

    # --- LDR Sensor Reading ---
    light_raw_adc = adc_ldr.read_u16()
    ldr_voltage = (light_raw_adc / config.ADC_MAX_VALUE) * config.ADC_VREF

    ldr_resistance = 0
    if ldr_voltage > 0.001 and ldr_voltage < (config.ADC_VREF - 0.001):
        ldr_resistance = config.LDR_DIVIDER_RESISTOR * (config.ADC_VREF - ldr_voltage) / ldr_voltage
    elif ldr_voltage <= 0.001:
        ldr_resistance = 10.0
    else:
        ldr_resistance = 1e7

    if ldr_resistance > 0:
        light_lux_estimated = (config.LUX_EST_M / ldr_resistance) + config.LUX_EST_C
    else:
        light_lux_estimated = config.MAX_EXPECTED_LUX

    if light_lux_estimated < 0: light_lux_estimated = 0
    if light_lux_estimated > config.MAX_EXPECTED_LUX: light_lux_estimated = config.MAX_EXPECTED_LUX

    light_percent = map_value(light_raw_adc, config.LDR_RAW_ADC_MAX, config.LDR_RAW_ADC_MIN, 0, 100)
    light_percent = max(0, min(100, int(light_percent)))


def send_sensor_data():
    """Sends sensor data to the server via HTTP POST."""
    global last_sensor_update_ms
    if not wlan.isconnected():
        print("SensorData: WiFi not connected.")
        return

    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.SENSOR_DATA_ENDPOINT_PATH}"

    payload = {
        "tower_id": config.TOWER_ID,
        "sensor_value": light_raw_adc,
        "light_level": light_percent,
        "lux": round(light_lux_estimated, 2),
        "faulty_lights": 1 if (fault_under_voltage or fault_over_voltage or fault_low_current or fault_high_current or fault_low_power) else 0,
        "maintenance_lights": 0,
        "efficiency": map_value(light_percent, 0, 100, 50, 95) if relay_state else 0,
        "voltage": round(voltage_actual, 2),
        "current": round(current_actual, 3),
        "power": round(power_actual, 2)
    }

    json_string = json.dumps(payload)
    headers = {'Content-Type': 'application/json'}

    print(f"Sending sensor data to {full_url}: {json_string}")

    http_response_code = 0
    response_text = ""
    for retry in range(config.MAX_HTTP_RETRIES):
        try:
            response = requests.post(full_url, data=json_string, headers=headers)
            http_response_code = response.status_code
            response_text = response.text
            response.close()
            if 200 <= http_response_code < 300:
                break
        except Exception as e:
            print(f"SensorData: HTTP POST failed, error: {e}. Retry {retry + 1}/{config.MAX_HTTP_RETRIES}")
            utime.sleep_ms(config.HTTP_RETRY_DELAY_MS)
            http_response_code = 0

    if 200 <= http_response_code < 300:
        print(f"Sensor data sent (HTTP {http_response_code})")
        blink_led(1, 50)
    else:
        print(f"Sensor data send FAILED (HTTP {http_response_code}). Response: {response_text}")

    last_sensor_update_ms = utime.ticks_ms()

def update_status():
    """Updates the operational status based on sensor data, time, and faults."""
    global current_status, fault_type, manual_override, relay_state

    if manual_override and utime.ticks_diff(utime.ticks_ms(), manual_override_start_time) > config.MANUAL_CONTROL_TIMEOUT_MS:
        manual_override = False
        print("Manual override timed out.")

    time_tuple = get_current_time_tuple()
    tm_hour = time_tuple[3]
    tm_min = time_tuple[4]

    is_night = False
    current_minute_of_day = tm_hour * 60 + tm_min
    night_start_minute_of_day = config.NIGHT_HOUR_START * 60 + config.NIGHT_MINUTE_START
    night_end_minute_of_day = config.NIGHT_HOUR_END * 60 + config.NIGHT_MINUTE_END

    if night_start_minute_of_day > night_end_minute_of_day:
        if current_minute_of_day >= night_start_minute_of_day or current_minute_of_day <= night_end_minute_of_day:
            is_night = True
    else:
        if night_start_minute_of_day <= current_minute_of_day <= night_end_minute_of_day:
            is_night = True

    is_low_light = (light_percent < config.LIGHT_PERCENT_DARK_THRESHOLD)

    should_be_on = False
    if manual_override:
        should_be_on = relay_state
    else:
        if is_night or is_low_light:
            should_be_on = True
        else:
            should_be_on = False

    if should_be_on != relay_state:
        control_relay(should_be_on)

    new_status = current_status
    new_fault_type = fault_type

    if fault_under_voltage or fault_over_voltage or fault_low_current or fault_high_current or fault_low_power:
        new_status = "faulty"
        if fault_under_voltage: new_fault_type = "undervoltage"
        elif fault_over_voltage: new_fault_type = "overvoltage"
        elif fault_low_current and voltage_actual > config.VOLTAGE_LOW_THRESHOLD: new_fault_type = "bulb_or_wiring"
        elif fault_high_current: new_fault_type = "overcurrent_or_short"
        elif fault_low_power and voltage_actual > config.VOLTAGE_LOW_THRESHOLD and current_actual > (config.CURRENT_LOW_THRESHOLD / 2.0):
            new_fault_type = "connection_issue"
        else: new_fault_type = "unknown_electrical"
    elif relay_state:
        new_status = "active"
        new_fault_type = ""
    else:
        new_status = "inactive"
        new_fault_type = ""

    if new_status != current_status or new_fault_type != fault_type:
        print(f"Status: {current_status} (Fault: {fault_type}) -> {new_status} (Fault: {new_fault_type})")
        current_status = new_status
        fault_type = new_fault_type

def send_status_update():
    """Sends status update (active, inactive, faulty) to the server."""
    global last_status_update_ms
    if not wlan.isconnected():
        print("StatusUpdate: WiFi not connected.")
        return

    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.STATUS_UPDATE_ENDPOINT_PATH}"

    payload = {
        "tower_id": config.TOWER_ID,
        "status": current_status
    }
    if current_status == "faulty" and fault_type:
        payload["fault_type"] = fault_type

    json_string = json.dumps(payload)
    headers = {'Content-Type': 'application/json'}

    print(f"Sending status update to {full_url}: {json_string}")

    http_response_code = 0
    response_text = ""
    for retry in range(config.MAX_HTTP_RETRIES):
        try:
            response = requests.post(full_url, data=json_string, headers=headers)
            http_response_code = response.status_code
            response_text = response.text
            response.close()
            if 200 <= http_response_code < 300:
                break
        except Exception as e:
            print(f"StatusUpdate: HTTP POST failed, error: {e}. Retry {retry + 1}/{config.MAX_HTTP_RETRIES}")
            utime.sleep_ms(config.HTTP_RETRY_DELAY_MS)
            http_response_code = 0

    if 200 <= http_response_code < 300:
        print(f"Status update sent (HTTP {http_response_code})")
    else:
        print(f"Status update FAILED (HTTP {http_response_code}). Response: {response_text}")

    last_status_update_ms = utime.ticks_ms()

def control_relay(state):
    """Controls the relay."""
    global relay_state
    # Active-Low relay: 0 = ON, 1 = OFF
    relay_pin.value(0 if state else 1)
    if relay_state != state:
        relay_state = state
        print(f"Relay state: {'ON' if state else 'OFF'} (Pin: {'LOW' if state else 'HIGH'})")

def check_faults():
    """Checks for fault conditions based on sensor readings."""
    global fault_under_voltage, fault_over_voltage, fault_low_current, fault_high_current, fault_low_power

    fault_under_voltage = (voltage_actual < config.VOLTAGE_LOW_THRESHOLD and voltage_actual > 0.5)
    fault_over_voltage = (voltage_actual > config.VOLTAGE_HIGH_THRESHOLD)

    fault_low_current = (relay_state and \
                         voltage_actual > config.VOLTAGE_LOW_THRESHOLD and \
                         current_actual < config.CURRENT_LOW_THRESHOLD and \
                         current_actual >= 0)

    fault_high_current = (current_actual > config.CURRENT_HIGH_THRESHOLD)

    fault_low_power = (relay_state and \
                       voltage_actual > config.VOLTAGE_LOW_THRESHOLD and \
                       current_actual > (config.CURRENT_LOW_THRESHOLD / 2.0) and \
                       power_actual < config.POWER_LOW_THRESHOLD)

def display_detailed_sensor_table():
    """Displays detailed sensor data in a clear table format for debugging."""
    print("\n" + "="*80)
    print("                    DETAILED SENSOR DATA TABLE")
    print("="*80)

    # Raw ADC Values Section
    print("RAW ADC VALUES:")
    print("-" * 50)
    print(f"  Voltage ADC Raw:     {adc_val_v_raw:>6d} / {int(config.ADC_MAX_VALUE):>5d} ({adc_val_v_raw/config.ADC_MAX_VALUE*100:>5.1f}%)")
    print(f"  Current ADC Raw:     {avg_adc_current_raw:>6.0f} / {int(config.ADC_MAX_VALUE):>5d} ({avg_adc_current_raw/config.ADC_MAX_VALUE*100:>5.1f}%)")
    print(f"  LDR ADC Raw:         {light_raw_adc:>6d} / {int(config.ADC_MAX_VALUE):>5d} ({light_raw_adc/config.ADC_MAX_VALUE*100:>5.1f}%)")

    # Intermediate Calculations Section
    print("\nINTERMEDIATE CALCULATIONS:")
    print("-" * 50)
    print(f"  Voltage ADC -> V:    {adc_voltage_v:.3f}V (ADC * {config.ADC_VREF}V / {int(config.ADC_MAX_VALUE)})")
    print(f"  Voltage Divider:     {voltage_divider_ratio:.2f}x (R1+R2)/R2 = ({config.VOLTAGE_R1:.0f}+{config.VOLTAGE_R2:.0f})/{config.VOLTAGE_R2:.0f}")
    print(f"  Current ADC -> V:    {vout_current_sensor:.3f}V (ADC * {config.ADC_VREF}V / {int(config.ADC_MAX_VALUE)})")
    print(f"  Current Zero Point:  {config.CURRENT_ZERO_POINT_VOLTAGE:.3f}V (calibrated no-load voltage)")
    print(f"  Current Scale:       {config.CURRENT_SCALE_FACTOR:.3f}V/A (ACS712 sensitivity)")
    print(f"  LDR ADC -> V:        {ldr_voltage:.3f}V")
    print(f"  LDR Resistance:      {ldr_resistance:.1f}Ω")

    # Final Calculated Values Section
    print("\nFINAL CALCULATED VALUES:")
    print("-" * 50)
    print(f"  Voltage:             {voltage_actual:.2f}V")
    print(f"  Current:             {current_actual:.3f}A")
    print(f"  Power:               {power_actual:.2f}W (V × I)")
    print(f"  Light Level:         {light_percent:>3d}% (mapped from ADC)")
    print(f"  Estimated Lux:       {light_lux_estimated:.1f} lux")

    # Thresholds and Status Section
    print("\nTHRESHOLDS & STATUS:")
    print("-" * 50)
    print(f"  Voltage Thresholds:  {config.VOLTAGE_LOW_THRESHOLD:.1f}V - {config.VOLTAGE_HIGH_THRESHOLD:.1f}V")
    print(f"  Current Thresholds:  {config.CURRENT_LOW_THRESHOLD:.2f}A - {config.CURRENT_HIGH_THRESHOLD:.2f}A")
    print(f"  Power Threshold:     {config.POWER_LOW_THRESHOLD:.1f}W minimum")
    print(f"  Light Dark Thresh:   {config.LIGHT_PERCENT_DARK_THRESHOLD}% (below = dark)")

    # Fault Status Section
    print("\nFAULT STATUS:")
    print("-" * 50)
    print(f"  Under Voltage:       {'YES' if fault_under_voltage else 'NO'} (< {config.VOLTAGE_LOW_THRESHOLD:.1f}V)")
    print(f"  Over Voltage:        {'YES' if fault_over_voltage else 'NO'} (> {config.VOLTAGE_HIGH_THRESHOLD:.1f}V)")
    print(f"  Low Current:         {'YES' if fault_low_current else 'NO'} (< {config.CURRENT_LOW_THRESHOLD:.2f}A when ON)")
    print(f"  High Current:        {'YES' if fault_high_current else 'NO'} (> {config.CURRENT_HIGH_THRESHOLD:.2f}A)")
    print(f"  Low Power:           {'YES' if fault_low_power else 'NO'} (< {config.POWER_LOW_THRESHOLD:.1f}W when ON)")

    # System Status Section
    print("\nSYSTEM STATUS:")
    print("-" * 50)
    print(f"  Tower Status:        {current_status.upper()}")
    print(f"  Fault Type:          {fault_type if fault_type else 'None'}")
    print(f"  Relay State:         {'ON' if relay_state else 'OFF'}")
    print(f"  Manual Override:     {'ACTIVE' if manual_override else 'OFF'}")
    if manual_override:
        time_left_ms = config.MANUAL_CONTROL_TIMEOUT_MS - utime.ticks_diff(utime.ticks_ms(), manual_override_start_time)
        print(f"  Manual Time Left:    {time_left_ms/60000.0:.1f} minutes")
    print(f"  WiFi Connected:      {'YES' if wlan.isconnected() else 'NO'}")
    print(f"  Command Polling:     {'ENABLED' if wlan.isconnected() else 'DISABLED'}")
    print(f"  Remote Control:      {'AVAILABLE' if wlan.isconnected() else 'UNAVAILABLE'}")

    print("="*80)

def display_status():
    """Prints the current status to the console."""
    global last_display_update_ms

    # Display the detailed sensor table
    display_detailed_sensor_table()

    # Also keep the original compact status display
    print("\n--- COMPACT STATUS ---")
    print(f"V:{voltage_actual:.2f} I:{current_actual:.3f} P:{power_actual:.2f} | LDR_ADC:{light_raw_adc} Lux:{light_lux_estimated:.1f} Light%:{light_percent}")
    print(f"State: {current_status} (Fault:{fault_type}) Relay:{'ON' if relay_state else 'OFF'} Manual:{'Active' if manual_override else 'Off'}", end="")
    if manual_override:
        time_left_ms = config.MANUAL_CONTROL_TIMEOUT_MS - utime.ticks_diff(utime.ticks_ms(), manual_override_start_time)
        print(f" ({time_left_ms/60000.0:.1f}m left)", end="")
    print("\n--------------")
    last_display_update_ms = utime.ticks_ms()

def set_manual_override(manual_state, new_relay_state):
    """Sets or clears manual override."""
    global manual_override, manual_override_start_time
    manual_override = manual_state
    if manual_override:
        manual_override_start_time = utime.ticks_ms()
        print(f"Manual override activated. Target relay state: {'ON' if new_relay_state else 'OFF'}.")
        control_relay(new_relay_state)
    else:
        print("Manual override deactivated. Returning to automatic control.")

def poll_for_commands():
    """Polls the server for pending commands."""
    if not wlan.isconnected():
        print("Command polling skipped: WiFi not connected")
        return  # Skip if not connected

    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.COMMAND_POLL_ENDPOINT_PATH}{config.TOWER_ID}/"

    print(f"Polling for commands at: {full_url}")

    try:
        response = requests.get(full_url, headers={'Content-Type': 'application/json'})

        print(f"Command poll response: HTTP {response.status_code}")

        if response.status_code == 200:
            response_data = response.json()
            commands = response_data.get('commands', [])

            print(f"Found {len(commands)} pending commands")

            for command in commands:
                command_id = command.get('id')
                command_type = command.get('command_type')
                parameters = command.get('parameters', {})

                print(f"Received command: ID={command_id}, Type={command_type}, Params={parameters}")

                # Acknowledge command receipt
                acknowledge_command(command_id)

                # Execute the command
                execute_command(command_id, command_type, parameters)

        elif response.status_code == 404:
            print("No pending commands (HTTP 404)")
        else:
            print(f"Command polling failed (HTTP {response.status_code})")
            print(f"Response: {response.text}")

        response.close()

    except Exception as e:
        print(f"Command polling error: {e}")
        import sys
        sys.print_exception(e)

def acknowledge_command(command_id):
    """Acknowledges command receipt to the server."""
    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.COMMAND_POLL_ENDPOINT_PATH}{config.TOWER_ID}/"

    payload = {
        "command_id": command_id,
        "response_type": "acknowledged"
    }

    try:
        response = requests.post(full_url, data=json.dumps(payload), headers={'Content-Type': 'application/json'})

        if response.status_code == 200:
            print(f"Command {command_id} acknowledged")
        else:
            print(f"Failed to acknowledge command {command_id} (HTTP {response.status_code})")

        response.close()

    except Exception as e:
        print(f"Command acknowledge error: {e}")

def complete_command(command_id, response_data=None):
    """Marks command as completed on the server."""
    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.COMMAND_POLL_ENDPOINT_PATH}{config.TOWER_ID}/"

    payload = {
        "command_id": command_id,
        "response_type": "completed"
    }

    if response_data:
        payload["response_data"] = response_data

    try:
        response = requests.post(full_url, data=json.dumps(payload), headers={'Content-Type': 'application/json'})

        if response.status_code == 200:
            print(f"Command {command_id} completed")
        else:
            print(f"Failed to complete command {command_id} (HTTP {response.status_code})")

        response.close()

    except Exception as e:
        print(f"Command complete error: {e}")

def execute_command(command_id, command_type, parameters):
    """Executes a received command."""
    print(f"Executing command: {command_type}")

    response_data = {}

    try:
        if command_type == "manual_override_on":
            relay_state_param = parameters.get("relay_state", True)
            set_manual_override(True, relay_state_param)
            response_data["relay_state"] = relay_state_param
            response_data["manual_override"] = True

        elif command_type == "manual_override_off":
            set_manual_override(False, False)
            response_data["manual_override"] = False

        elif command_type == "maintenance_mode":
            # Set to maintenance mode - turn off and disable automatic control
            set_manual_override(True, False)
            response_data["maintenance_mode"] = True
            response_data["relay_state"] = False

        elif command_type == "reset":
            # Reset manual override and return to automatic control
            set_manual_override(False, False)
            response_data["reset"] = True
            response_data["manual_override"] = False

        elif command_type == "get_status":
            # Return current status
            response_data["status"] = current_status
            response_data["relay_state"] = relay_state
            response_data["manual_override"] = manual_override
            response_data["voltage"] = voltage_actual
            response_data["current"] = current_actual
            response_data["power"] = power_actual
            response_data["light_percent"] = light_percent

        else:
            print(f"Unknown command type: {command_type}")
            # Mark command as failed
            fail_command(command_id, f"Unknown command type: {command_type}")
            return

        # Mark command as completed
        complete_command(command_id, response_data)

        print(f"Command {command_type} executed successfully")

    except Exception as e:
        print(f"Command execution error: {e}")
        fail_command(command_id, str(e))

def fail_command(command_id, error_message):
    """Marks command as failed on the server."""
    server_protocol = "https://" if config.USE_HTTPS else "http://"
    full_url = f"{server_protocol}{config.SERVER_HOST}:{config.SERVER_PORT}{config.API_BASE_PATH}{config.COMMAND_POLL_ENDPOINT_PATH}{config.TOWER_ID}/"

    payload = {
        "command_id": command_id,
        "response_type": "failed",
        "error_message": error_message
    }

    try:
        response = requests.post(full_url, data=json.dumps(payload), headers={'Content-Type': 'application/json'})
        response.close()
    except Exception as e:
        print(f"Command fail error: {e}")

# --- Main Execution ---
def main():
    global last_sensor_update_ms, last_status_update_ms, last_display_update_ms, last_command_poll_ms

    print("\n\n=== Pico W Tower Light Controller Starting ===")
    print(f"Tower ID: {config.TOWER_ID}, Firmware: {config.FIRMWARE_VERSION}")

    relay_pin.off()
    status_led_pin.off()

    if not connect_to_wifi():
        print("WiFi connection failed. Retrying in background or limited functionality.")

    update_sensors()
    display_status()

    current_time_ms = utime.ticks_ms()
    last_sensor_update_ms = current_time_ms
    last_status_update_ms = current_time_ms
    last_display_update_ms = current_time_ms
    last_command_poll_ms = current_time_ms

    print("Initial setup complete. Entering main loop.")

    while True:
        current_time_ms = utime.ticks_ms()

        if not wlan.isconnected():
            print("WiFi connection lost. Attempting to reconnect...")
            if not connect_to_wifi():
                print("Reconnect failed. Will retry later. Delaying loop.")
                utime.sleep_ms(config.WIFI_RETRY_DELAY_MS)
                continue
            else:
                print("Reconnected to WiFi.")

        update_sensors()
        check_faults()
        update_status()

        if utime.ticks_diff(current_time_ms, last_sensor_update_ms) >= config.DATA_SEND_INTERVAL_MS:
            send_sensor_data()

        if utime.ticks_diff(current_time_ms, last_status_update_ms) >= config.STATUS_UPDATE_INTERVAL_MS:
            send_status_update()

        if utime.ticks_diff(current_time_ms, last_display_update_ms) >= config.DISPLAY_UPDATE_INTERVAL_MS:
            display_status()

        if utime.ticks_diff(current_time_ms, last_command_poll_ms) >= config.COMMAND_POLL_INTERVAL_MS:
            poll_for_commands()
            last_command_poll_ms = current_time_ms

        utime.sleep_ms(config.READ_INTERVAL_MS)

if __name__ == "__main__":
    main()
