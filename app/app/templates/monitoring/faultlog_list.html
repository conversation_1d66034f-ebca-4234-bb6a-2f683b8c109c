{% extends 'base.html' %}

{% block title %}Fault Logs - Tower Light Monitoring{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8 animate-fade-in">
        <div class="flex justify-between items-center mb-8">
            <h1 class="text-3xl font-bold text-white">Fault Logs</h1>
            <div class="flex space-x-4">
                <button class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-lg transition duration-200">
                    Export Data
                </button>
            </div>
        </div>

        <div class="bg-surface rounded-xl overflow-hidden shadow-lg animate-slide-up">
            <div class="p-6 border-b border-accent">
                <form method="get" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
                    <div class="space-y-2">
                        <label for="tower" class="block text-sm font-medium text-gray-300">Tower</label>
                        <select id="tower" name="tower" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                            <option value="">All Towers</option>
                            {% for tower in towers %}
                                <option value="{{ tower.id }}">{{ tower.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="space-y-2">
                        <label for="fault_type" class="block text-sm font-medium text-gray-300">Fault Type</label>
                        <select id="fault_type" name="fault_type" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                            <option value="">All Types</option>
                            {% for fault_type in fault_types %}
                                <option value="{{ fault_type.0 }}">{{ fault_type.1 }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="space-y-2">
                        <label for="start_date" class="block text-sm font-medium text-gray-300">Start Date</label>
                        <input type="date" id="start_date" name="start_date" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                    </div>
                    <div class="space-y-2">
                        <label for="end_date" class="block text-sm font-medium text-gray-300">End Date</label>
                        <input type="date" id="end_date" name="end_date" class="w-full bg-accent text-gray-300 rounded-lg border-0 focus:ring-2 focus:ring-primary py-2 px-3">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="w-full bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-lg transition duration-200 flex items-center justify-center">
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"/>
                            </svg>
                            Filter
                        </button>
                    </div>
                </form>
            </div>

            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-accent">
                    <thead class="bg-surface-dark">
                    <tr>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Tower
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Fault Type
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Description
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Timestamp
                        </th>
                        <th scope="col" class="px-6 py-4 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                            Status
                        </th>
                    </tr>
                    </thead>
                    <tbody class="divide-y divide-accent">
                    {% for log in fault_logs %}
                        <tr class="hover:bg-accent transition duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-200">{{ log.tower.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-300">{{ log.get_fault_type_display }}</div>
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-300">{{ log.description|truncatechars:50 }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-400">
                                {{ log.timestamp|date:"F j, Y, g:i a" }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full {% if log.resolved %}bg-green-900 text-green-200{% else %}bg-red-900 text-red-200{% endif %}">
                                {{ log.resolved|yesno:"Resolved,Active" }}
                            </span>
                            </td>
                        </tr>
                    {% empty %}
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-400">
                                No fault logs found.
                            </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>

            {% if is_paginated %}
                <div class="bg-surface-dark px-6 py-4 border-t border-accent">
                    <div class="flex items-center justify-between">
                        <p class="text-sm text-gray-400">
                            Showing
                            <span class="font-medium text-white">{{ page_obj.start_index }}</span>
                            to
                            <span class="font-medium text-white">{{ page_obj.end_index }}</span>
                            of
                            <span class="font-medium text-white">{{ page_obj.paginator.count }}</span>
                            results
                        </p>
                        <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                            {% if page_obj.has_previous %}
                                <a href="?page={{ page_obj.previous_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-l-lg border border-accent bg-surface text-sm font-medium text-gray-300 hover:bg-accent">
                                    Previous
                                </a>
                            {% endif %}

                            {% for i in paginator.page_range %}
                                {% if page_obj.number == i %}
                                    <span class="relative inline-flex items-center px-4 py-2 border border-accent bg-primary text-sm font-medium text-white">
                                {{ i }}
                            </span>
                                {% else %}
                                    <a href="?page={{ i }}" class="relative inline-flex items-center px-4 py-2 border border-accent bg-surface text-sm font-medium text-gray-300 hover:bg-accent">
                                        {{ i }}
                                    </a>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                                <a href="?page={{ page_obj.next_page_number }}" class="relative inline-flex items-center px-3 py-2 rounded-r-lg border border-accent bg-surface text-sm font-medium text-gray-300 hover:bg-accent">
                                    Next
                                </a>
                            {% endif %}
                        </nav>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>

    <script>
        // Add smooth transitions and animations
        document.addEventListener('DOMContentLoaded', () => {
            // Animate table rows on hover
            const rows = document.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseover', () => {
                    row.classList.add('transform', 'scale-[1.01]', 'transition-transform');
                });
                row.addEventListener('mouseout', () => {
                    row.classList.remove('transform', 'scale-[1.01]');
                });
            });

            // Animate form controls on focus
            const formControls = document.querySelectorAll('input, select');
            formControls.forEach(control => {
                control.addEventListener('focus', () => {
                    control.classList.add('ring-2', 'ring-primary', 'transition-all');
                });
                control.addEventListener('blur', () => {
                    control.classList.remove('ring-2', 'ring-primary');
                });
            });
        });
    </script>
{% endblock %}

