# Generated by Django 4.2.11 on 2025-04-19 19:01

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SensorData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('sensor_value', models.IntegerField(help_text='Raw sensor value')),
                ('light_level', models.IntegerField(help_text='Light level percentage (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('faulty_lights', models.IntegerField(default=0, help_text='Number of faulty lights detected')),
                ('maintenance_lights', models.IntegerField(default=0, help_text='Number of lights needing maintenance')),
                ('efficiency', models.IntegerField(help_text='Efficiency percentage (0-100)', validators=[django.core.validators.MinValueValidator(0), django.core.validators.MaxValueValidator(100)])),
                ('tower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sensor_data', to='monitoring.towerlight')),
            ],
            options={
                'verbose_name_plural': 'Sensor Data',
                'ordering': ['-timestamp'],
            },
        ),
    ]
