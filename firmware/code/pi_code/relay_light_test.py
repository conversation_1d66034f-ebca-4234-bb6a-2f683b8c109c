# relay_light_test.py
# Simple test to verify relay wiring and light-based switching
# Relay turns ON when light is low, OFF when light is high

import machine
import utime

# --- Configuration ---
TOWER_ID = 1  # Change to 2 for second microcontroller

# Pin Definitions
LDR_PIN_ADC_NUM = 2         # ADC2 is GP28 for LDR
RELAY_PIN_NUM = 21          # GP21 for relay control
STATUS_LED_PIN_NUM = "LED"  # Onboard LED

# ADC Configuration
ADC_MAX_VALUE = 65535.0     # Max value for 16-bit ADC
ADC_VREF = 3.3              # Pico W ADC reference voltage

# LDR Configuration
LDR_RAW_ADC_MIN = 3000      # Bright light (low resistance)
LDR_RAW_ADC_MAX = 60000     # Dark (high resistance)

# Light Thresholds
LIGHT_DARK_THRESHOLD = 30   # Below this % = dark (relay ON)
LIGHT_BRIGHT_THRESHOLD = 50 # Above this % = bright (relay OFF)
# Hysteresis prevents rapid switching

# Timing
READ_INTERVAL_MS = 1000     # Read sensors every 1 second
DISPLAY_INTERVAL_MS = 2000  # Display status every 2 seconds

# --- Pin Initialization ---
adc_ldr = machine.ADC(LDR_PIN_ADC_NUM)
relay_pin = machine.Pin(RELAY_PIN_NUM, machine.Pin.OUT)
status_led_pin = machine.Pin(STATUS_LED_PIN_NUM, machine.Pin.OUT)

# --- Global Variables ---
relay_state = False
light_percent = 0
light_raw_adc = 0
last_display_time = 0

def map_value(x, in_min, in_max, out_min, out_max):
    """Maps a value from one range to another."""
    if in_max == in_min:
        return out_min
    return (x - in_min) * (out_max - out_min) / (in_max - in_min) + out_min

def control_relay(state):
    """Controls the relay with active-low logic."""
    global relay_state
    # Active-Low relay: 0 = ON, 1 = OFF
    relay_pin.value(0 if state else 1)
    if relay_state != state:
        relay_state = state
        print(f"🔌 RELAY: {'ON' if state else 'OFF'} (Pin: {'LOW' if state else 'HIGH'})")
        # Blink LED to indicate relay change
        blink_status_led(3 if state else 1)

def blink_status_led(times):
    """Blinks the status LED."""
    for _ in range(times):
        status_led_pin.on()
        utime.sleep_ms(100)
        status_led_pin.off()
        utime.sleep_ms(100)

def read_light_sensor():
    """Reads the LDR sensor and calculates light percentage."""
    global light_percent, light_raw_adc
    
    # Read raw ADC value
    light_raw_adc = adc_ldr.read_u16()
    
    # Convert to percentage (0% = dark, 100% = bright)
    light_percent = map_value(light_raw_adc, LDR_RAW_ADC_MAX, LDR_RAW_ADC_MIN, 0, 100)
    light_percent = max(0, min(100, int(light_percent)))

def update_relay_based_on_light():
    """Updates relay state based on light intensity with hysteresis."""
    global relay_state
    
    if relay_state:
        # Relay is currently ON, turn OFF only if light gets bright
        if light_percent > LIGHT_BRIGHT_THRESHOLD:
            control_relay(False)
            print(f"💡 Light is bright ({light_percent}%) - Turning relay OFF")
    else:
        # Relay is currently OFF, turn ON only if light gets dark
        if light_percent < LIGHT_DARK_THRESHOLD:
            control_relay(True)
            print(f"🌙 Light is dark ({light_percent}%) - Turning relay ON")

def display_status():
    """Displays current sensor readings and relay state."""
    global last_display_time
    
    current_time = utime.ticks_ms()
    if utime.ticks_diff(current_time, last_display_time) >= DISPLAY_INTERVAL_MS:
        print("\n" + "="*60)
        print(f"🏗️  TOWER {TOWER_ID} - RELAY LIGHT TEST")
        print("="*60)
        print(f"📊 RAW ADC:      {light_raw_adc:>6d} / {int(ADC_MAX_VALUE):>5d} ({light_raw_adc/ADC_MAX_VALUE*100:>5.1f}%)")
        print(f"💡 LIGHT LEVEL:  {light_percent:>3d}% ({'DARK' if light_percent < LIGHT_DARK_THRESHOLD else 'BRIGHT' if light_percent > LIGHT_BRIGHT_THRESHOLD else 'MEDIUM'})")
        print(f"🔌 RELAY STATE:  {'ON' if relay_state else 'OFF'} (Pin: {'LOW' if relay_state else 'HIGH'})")
        print(f"⚙️  THRESHOLDS:   Dark < {LIGHT_DARK_THRESHOLD}% | Bright > {LIGHT_BRIGHT_THRESHOLD}%")
        
        # Visual light bar
        bar_length = 20
        filled = int((light_percent / 100) * bar_length)
        bar = "█" * filled + "░" * (bar_length - filled)
        print(f"📈 LIGHT BAR:    |{bar}| {light_percent}%")
        
        print("="*60)
        last_display_time = current_time

def test_relay_manual():
    """Manual relay test - toggle every 3 seconds for 10 cycles."""
    print("\n🧪 MANUAL RELAY TEST - 10 cycles")
    print("Watch the relay and any connected lights...")
    
    for i in range(10):
        state = (i % 2 == 0)  # Alternate ON/OFF
        control_relay(state)
        print(f"Cycle {i+1}/10: Relay {'ON' if state else 'OFF'}")
        utime.sleep(3)
    
    control_relay(False)  # Ensure OFF at end
    print("✅ Manual test complete\n")

def main():
    """Main test loop."""
    print("\n🚀 STARTING RELAY LIGHT TEST")
    print(f"Tower ID: {TOWER_ID}")
    print("="*60)
    print("📋 TEST DESCRIPTION:")
    print(f"• Relay turns ON when light < {LIGHT_DARK_THRESHOLD}%")
    print(f"• Relay turns OFF when light > {LIGHT_BRIGHT_THRESHOLD}%")
    print("• Hysteresis prevents rapid switching")
    print("• Active-LOW relay logic (LOW = ON, HIGH = OFF)")
    print("="*60)
    
    # Initialize
    relay_pin.off()  # Start with relay OFF (HIGH for active-low)
    status_led_pin.off()
    
    # Optional: Run manual test first
    user_input = input("Run manual relay test first? (y/n): ")
    if user_input.lower() == 'y':
        test_relay_manual()
    
    print("🔄 Starting automatic light-based control...")
    print("💡 Cover/uncover the LDR sensor to test switching")
    print("🛑 Press Ctrl+C to stop\n")
    
    try:
        while True:
            # Read light sensor
            read_light_sensor()
            
            # Update relay based on light
            update_relay_based_on_light()
            
            # Display status
            display_status()
            
            # Wait before next reading
            utime.sleep_ms(READ_INTERVAL_MS)
            
    except KeyboardInterrupt:
        print("\n🛑 Test stopped by user")
        control_relay(False)  # Ensure relay is OFF
        print("✅ Relay turned OFF - Test complete")

if __name__ == "__main__":
    main()
