{% extends 'base.html' %}

{% block title %}Control Panel - Tower Light Monitoring{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-white mb-8">Control Panel</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for tower in towers %}
                <div class="bg-surface rounded-xl shadow-lg p-6" id="tower-{{ tower.id }}">
                    <h2 class="text-xl font-semibold text-white mb-4">
                        {{ tower.name }}
                        {% if tower.id <= 2 %}
                            <span class="text-xs bg-blue-600 text-white px-2 py-1 rounded ml-2">MCU</span>
                        {% else %}
                            <span class="text-xs bg-gray-600 text-white px-2 py-1 rounded ml-2">SIM</span>
                        {% endif %}
                    </h2>
                    <p class="mb-2 text-gray-300">Status: <span class="font-medium text-white" id="status-{{ tower.id }}">{{ tower.get_status_display }}</span></p>
                    <p class="mb-2 text-gray-300">Last checked: {{ tower.last_checked|date:"F j, Y, g:i a" }}</p>

                    <!-- Command Status Display for Microcontrollers -->
                    {% if tower.id <= 2 %}
                        <div class="mb-4 p-2 bg-gray-800 rounded" id="command-status-{{ tower.id }}" style="display: none;">
                            <p class="text-xs text-yellow-400">Command Status: <span id="command-text-{{ tower.id }}"></span></p>
                        </div>
                    {% endif %}

                    <div class="flex space-x-2">
                        <button class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'turn_on')" id="btn-on-{{ tower.id }}">
                            Turn On
                        </button>
                        <button class="bg-secondary hover:bg-secondary-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'turn_off')" id="btn-off-{{ tower.id }}">
                            Turn Off
                        </button>
                        <button class="bg-warning hover:bg-warning-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'maintenance')" id="btn-maint-{{ tower.id }}">
                            Maintenance
                        </button>
                    </div>

                    {% if tower.id <= 2 %}
                        <div class="mt-2">
                            <button class="bg-gray-600 hover:bg-gray-700 text-white font-bold py-1 px-3 rounded text-sm w-full"
                                    onclick="resetTower('{{ tower.id }}')">
                                Reset MCU
                            </button>
                        </div>
                    {% endif %}
                </div>
            {% endfor %}
        </div>
    </div>

    <script>
        // Track command status for microcontrollers
        let commandTracking = {};

        function controlTower(towerId, action) {
            // Disable buttons during command execution
            disableButtons(towerId, true);

            fetch(`/api/towers/${towerId}/control/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({ action: action })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update status immediately
                        updateTowerStatus(towerId, data.tower_status);

                        // For microcontrollers (towers 1-2), track command status
                        if (towerId <= 2 && data.command_id) {
                            showCommandStatus(towerId, `Command ${action} queued...`);
                            commandTracking[towerId] = {
                                commandId: data.command_id,
                                action: action,
                                startTime: Date.now()
                            };
                            // Start polling for command status
                            pollCommandStatus(towerId);
                        } else {
                            // For simulated towers, just show success
                            showNotification(`Tower ${towerId} ${action} successful`, 'success');
                            disableButtons(towerId, false);
                        }
                    } else {
                        alert(`Error: ${data.message}`);
                        disableButtons(towerId, false);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                    disableButtons(towerId, false);
                });
        }

        function resetTower(towerId) {
            if (confirm(`Are you sure you want to reset Tower ${towerId}?`)) {
                controlTower(towerId, 'reset');
            }
        }

        function disableButtons(towerId, disabled) {
            const buttons = [
                `btn-on-${towerId}`,
                `btn-off-${towerId}`,
                `btn-maint-${towerId}`
            ];

            buttons.forEach(btnId => {
                const btn = document.getElementById(btnId);
                if (btn) {
                    btn.disabled = disabled;
                    btn.style.opacity = disabled ? '0.5' : '1';
                }
            });
        }

        function updateTowerStatus(towerId, status) {
            const statusElement = document.getElementById(`status-${towerId}`);
            if (statusElement) {
                statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
            }
        }

        function showCommandStatus(towerId, message) {
            const statusDiv = document.getElementById(`command-status-${towerId}`);
            const textSpan = document.getElementById(`command-text-${towerId}`);

            if (statusDiv && textSpan) {
                textSpan.textContent = message;
                statusDiv.style.display = 'block';
            }
        }

        function hideCommandStatus(towerId) {
            const statusDiv = document.getElementById(`command-status-${towerId}`);
            if (statusDiv) {
                statusDiv.style.display = 'none';
            }
        }

        function pollCommandStatus(towerId) {
            const tracking = commandTracking[towerId];
            if (!tracking) return;

            // Timeout after 2 minutes
            if (Date.now() - tracking.startTime > 120000) {
                showCommandStatus(towerId, 'Command timeout');
                disableButtons(towerId, false);
                delete commandTracking[towerId];
                setTimeout(() => hideCommandStatus(towerId), 3000);
                return;
            }

            // Poll every 2 seconds
            setTimeout(() => {
                fetch(`/api/tower-commands/${towerId}/`)
                    .then(response => response.json())
                    .then(data => {
                        // Check if our command is still pending
                        const ourCommand = data.commands?.find(cmd => cmd.id === tracking.commandId);

                        if (!ourCommand) {
                            // Command completed or expired
                            showCommandStatus(towerId, `Command ${tracking.action} completed`);
                            showNotification(`Tower ${towerId} ${tracking.action} completed`, 'success');
                            disableButtons(towerId, false);
                            delete commandTracking[towerId];
                            setTimeout(() => hideCommandStatus(towerId), 3000);
                        } else {
                            // Command still pending, continue polling
                            showCommandStatus(towerId, `Command ${tracking.action} pending...`);
                            pollCommandStatus(towerId);
                        }
                    })
                    .catch(error => {
                        console.error('Error polling command status:', error);
                        showCommandStatus(towerId, 'Command status unknown');
                        disableButtons(towerId, false);
                        delete commandTracking[towerId];
                        setTimeout(() => hideCommandStatus(towerId), 3000);
                    });
            }, 2000);
        }

        function showNotification(message, type = 'info') {
            // Simple notification system
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded shadow-lg z-50 ${
                type === 'success' ? 'bg-green-600' :
                type === 'error' ? 'bg-red-600' : 'bg-blue-600'
            } text-white`;
            notification.textContent = message;

            document.body.appendChild(notification);

            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // Auto-refresh page every 30 seconds to update tower statuses
        setInterval(() => {
            // Only refresh if no commands are being tracked
            if (Object.keys(commandTracking).length === 0) {
                location.reload();
            }
        }, 30000);
    </script>
{% endblock %}

