# Generated by Django 4.2.11 on 2025-06-08 16:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('monitoring', '0004_change_efficiency_to_float'),
    ]

    operations = [
        migrations.CreateModel(
            name='TowerCommand',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('command_type', models.CharField(choices=[('turn_on', 'Turn On'), ('turn_off', 'Turn Off'), ('maintenance_mode', 'Maintenance Mode'), ('reset', 'Reset'), ('manual_override_on', 'Manual Override On'), ('manual_override_off', 'Manual Override Off'), ('get_status', 'Get Status')], max_length=20)),
                ('parameters', models.JSONField(blank=True, default=dict, help_text='Additional command parameters')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('sent', 'Sent'), ('acknowledged', 'Acknowledged'), ('completed', 'Completed'), ('failed', 'Failed'), ('expired', 'Expired')], default='pending', max_length=15)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('sent_at', models.DateTimeField(blank=True, null=True)),
                ('acknowledged_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('expires_at', models.DateTimeField(help_text='Command expires after this time')),
                ('response_data', models.JSONField(blank=True, default=dict, help_text='Response from microcontroller')),
                ('error_message', models.TextField(blank=True, help_text='Error message if command failed')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='tower_commands', to=settings.AUTH_USER_MODEL)),
                ('tower', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commands', to='monitoring.towerlight')),
            ],
            options={
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['tower', 'status'], name='monitoring__tower_i_108e53_idx'), models.Index(fields=['status', 'expires_at'], name='monitoring__status_b81ab5_idx')],
            },
        ),
    ]
