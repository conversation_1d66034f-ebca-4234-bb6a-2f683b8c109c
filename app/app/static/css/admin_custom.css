:root {
    --primary: #3D8FCA;
    --secondary: #FF574D;
    --warning: #FFC85C;
    --background: #F4F5F7;
    --accent: #EAEFF4;
    --dark: #23272A;
}

.navbar-primary {
    background-color: var(--primary) !important;
}

.sidebar-dark-primary {
    background-color: var(--dark) !important;
}

.btn-primary {
    background-color: var(--primary) !important;
    border-color: var(--primary) !important;
}

.btn-primary:hover {
    background-color: #3278A8 !important;
    border-color: #3278A8 !important;
}

.btn-secondary {
    background-color: var(--secondary) !important;
    border-color: var(--secondary) !important;
}

.btn-secondary:hover {
    background-color: #E54D44 !important;
    border-color: #E54D44 !important;
}

.btn-warning {
    background-color: var(--warning) !important;
    border-color: var(--warning) !important;
}

.btn-warning:hover {
    background-color: #E5B652 !important;
    border-color: #E5B652 !important;
}

.card-primary:not(.card-outline) > .card-header {
    background-color: var(--primary);
}

.page-item.active .page-link {
    background-color: var(--primary);
    border-color: var(--primary);
}

.page-link {
    color: var(--primary);
}

.page-link:hover {
    color: #3278A8;
}

.alert-info {
    background-color: var(--accent);
    border-color: var(--primary);
    color: var(--dark);
}

.alert-warning {
    background-color: var(--warning);
    border-color: #E5B652;
    color: var(--dark);
}

.alert-danger {
    background-color: var(--secondary);
    border-color: #E54D44;
    color: white;
}

