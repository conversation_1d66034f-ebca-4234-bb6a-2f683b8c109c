# Generated by Django 4.2.11 on 2025-04-19 20:53

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('monitoring', '0002_sensordata'),
    ]

    operations = [
        migrations.AddField(
            model_name='sensordata',
            name='current',
            field=models.FloatField(blank=True, help_text='Current in amperes', null=True),
        ),
        migrations.AddField(
            model_name='sensordata',
            name='power',
            field=models.FloatField(blank=True, help_text='Power in watts', null=True),
        ),
        migrations.AddField(
            model_name='sensordata',
            name='voltage',
            field=models.FloatField(blank=True, help_text='Voltage in volts', null=True),
        ),
    ]
