# Tower Light Monitoring System Firmware

## Overview
This firmware is designed for ESP32-based tower light monitoring and control systems. It provides automated control of tower lights based on time of day, ambient light conditions, and fault detection. The system communicates with a central monitoring server to report sensor data and status updates, enabling real-time monitoring and fault detection for tower lights.

## Features
- Automatic light control based on:
  - Time of day (18:00-06:00 by default, configurable)
  - Ambient light conditions (LDR sensor)
- Real-time sensor monitoring:
  - Voltage monitoring (0-15V range)
  - Current sensing (0-5A range)
  - Light level detection (LDR)
  - Power calculation and efficiency metrics
- Comprehensive fault detection:
  - Under voltage (<10.0V)
  - Over voltage (>12.5V)
  - Low current when active (<0.02A)
  - High current (>2.5A)
  - Low power when active (<2.0W)
- Real-time data reporting to monitoring server
- Manual override capability with timeout
- NTP time synchronization
- Status LED for visual feedback
- Detailed serial output for debugging

## Hardware Requirements
- ESP32 Development Board
- Voltage Sensor (Voltage Divider with 30kΩ and 7.5kΩ resistors)
- Current Sensor (ACS712 5A version)
- Light Dependent Resistor (LDR) with 10kΩ pull-down resistor
- 5V Relay Module
- Status LED (built-in LED on GPIO2 or external)
- 12V DC Power Supply
- Tower Light (12V DC)

### Pin Configuration
```
VOLTAGE_PIN     : GPIO22 (ADC1_CH6)
CURRENT_PIN     : GPIO34 (ADC1_CH7)
LDR_PIN         : GPIO33 (ADC1_CH5)
RELAY_PIN       : GPIO21
STATUS_LED_PIN  : GPIO2
```

## Software Dependencies
- ESP32 Arduino Core
- WiFi Library
- HTTPClient
- ArduinoJson
- time.h (for NTP time synchronization)

## Configuration
All configurable parameters are in `config.h`:

### Network Configuration
```cpp
// WiFi Credentials
#define WIFI_SSID "your_ssid"
#define WIFI_PASSWORD "your_password"
#define WIFI_RETRY_DELAY 5000
#define WIFI_MAX_RETRIES 20
#define WIFI_CONNECT_TIMEOUT_MS 20000

// Server Configuration
#define SERVER_HOST "192.168.x.x"
#define SERVER_PORT 8000
#define USE_HTTPS false

// API Endpoints
#define API_BASE_PATH "/api"
#define SENSOR_DATA_ENDPOINT_PATH "/monitoring/sensor-data/"
#define STATUS_UPDATE_ENDPOINT_PATH "/tower-status-update/"
```

### Device Configuration
```cpp
#define TOWER_ID 1
#define FIRMWARE_VERSION "1.0.4"
```

### Sensor Calibration
```cpp
// Voltage Sensor
#define VOLTAGE_R1 30000.0f
#define VOLTAGE_R2 7500.0f
#define VOLTAGE_SENSE_MAX_EXPECTED 12.3f
#define VOLTAGE_SENSE_NOISE_THRESHOLD 0.1f

// Current Sensor
#define CURRENT_SCALE_FACTOR 0.185f
#define CURRENT_NUM_SAMPLES 1000
#define CURRENT_ZERO_POINT 1.235f
#define CURRENT_MAX_EXPECTED 1.5f
#define CURRENT_NOISE_THRESHOLD 0.05f

// LDR Sensor
#define LDR_DIVIDER_RESISTOR 10000.0f
#define LDR_RAW_ADC_MIN 18
#define LDR_RAW_ADC_MAX 4095
```

### Control Parameters
```cpp
// Fault Thresholds
#define VOLTAGE_LOW_THRESHOLD 10.0f
#define VOLTAGE_HIGH_THRESHOLD 12.5f
#define CURRENT_LOW_THRESHOLD 0.02f
#define CURRENT_HIGH_THRESHOLD 2.50f
#define POWER_LOW_THRESHOLD 2.0f
#define LIGHT_PERCENT_DARK_THRESHOLD 20

// Night Hours (24-hour format)
#define NIGHT_HOUR_START 18
#define NIGHT_MINUTE_START 0
#define NIGHT_HOUR_END 6
#define NIGHT_MINUTE_END 0
```

### Timing Configuration
```cpp
#define READ_INTERVAL_MS 200
#define DATA_SEND_INTERVAL_MS 30000
#define STATUS_UPDATE_INTERVAL_MS 60000
#define DISPLAY_UPDATE_INTERVAL_MS 10000
#define MANUAL_CONTROL_TIMEOUT_MS 300000
```

## Main Components

### 1. Sensor Management
- Reads voltage through a voltage divider on GPIO22
- Samples current using ACS712 sensor on GPIO34
- Measures ambient light using LDR on GPIO33
- Calculates power as voltage × current
- Applies noise filtering and threshold detection

### 2. Automatic Control Logic
- Uses NTP to synchronize time with internet time servers
- Automatically turns lights ON during night hours (18:00-06:00 by default)
- Turns lights ON when ambient light falls below threshold
- Turns lights OFF during daytime or when faults are detected
- Supports manual override with 5-minute timeout

### 3. Fault Detection
Monitors and reports:
- Under voltage: voltage < 10.0V (configurable)
- Over voltage: voltage > 12.5V (configurable)
- Low current when active: current < 0.02A (configurable)
- High current: current > 2.5A (configurable)
- Low power when active: power < 2.0W (configurable)

### 4. Communication
- Sends sensor data every 30 seconds (configurable)
- Reports status changes every 60 seconds (configurable)
- Sends immediate updates when faults are detected
- Uses HTTP POST requests with JSON payloads
- Implements retry logic for failed communications

## API Integration

### Sensor Data Endpoint
Sends sensor data to `/api/monitoring/sensor-data/` with the following JSON payload:
```json
{
  "tower_id": 1,
  "sensor_value": 3500,
  "light_level": 85,
  "lux": 1200,
  "faulty_lights": 0,
  "maintenance_lights": 0,
  "efficiency": 92,
  "voltage": 12.1,
  "current": 0.75,
  "power": 9.08
}
```

### Status Update Endpoint
Reports status changes to `/api/tower-status-update/` with the following JSON payload:
```json
{
  "tower_id": 1,
  "status": "active"
}
```

When a fault is detected, additional information is included:
```json
{
  "tower_id": 1,
  "status": "faulty",
  "fault_type": "undervoltage"
}
```

## Fault Types
The firmware can detect and report the following fault types:
- `undervoltage`: Voltage below minimum threshold
- `overvoltage`: Voltage above maximum threshold
- `bulb_or_wiring`: Low current when voltage is normal
- `overcurrent_or_short`: Current above maximum threshold
- `connection_issue`: Low power when voltage and current are present
- `unknown_electrical`: Other electrical issues

## Error Handling
- WiFi connection management with automatic reconnection
- HTTP request retries (configurable via MAX_HTTP_RETRIES)
- Sensor reading validation with noise filtering
- Manual override timeout (5 minutes by default)
- Detailed serial logging for troubleshooting

## Serial Output
The firmware provides detailed serial output at 115200 baud:
```
--- Status ---
V:12.10 I:0.750 P:9.08 | LDR:3500 Lux:1200.0 Light%:85
State: active (Fault:) Relay:ON Manual:Off
--------------
```

This includes:
- Voltage, current, and power readings
- LDR raw value, calculated lux, and light percentage
- System state and fault information
- Relay and manual override status

## Installation and Setup

1. Hardware Setup:
   - Assemble the hardware according to the wiring diagram in HARDWARE_SETUP.md
   - Connect the ESP32 to your computer via USB

2. Configure `config.h` with your settings:
   ```cpp
   // WiFi settings
   #define WIFI_SSID "your_ssid"
   #define WIFI_PASSWORD "your_password"

   // Server settings
   #define SERVER_HOST "192.168.x.x"
   #define SERVER_PORT 8000

   // Tower identification
   #define TOWER_ID 1
   ```

3. Upload firmware to ESP32:
   - Open the project in Arduino IDE
   - Select "ESP32 Dev Module" as the board
   - Set the correct port
   - Compile and upload the firmware

4. Calibration:
   - Follow the calibration steps in HARDWARE_SETUP.md
   - Adjust sensor parameters in config.h if needed
   - Verify readings match physical measurements

5. Monitor operation:
   - Open Serial Monitor at 115200 baud
   - Verify WiFi connection is established
   - Check sensor readings are within expected ranges
   - Confirm communication with the server

## Troubleshooting

### WiFi Connection Issues
- Check WIFI_SSID and WIFI_PASSWORD in config.h
- Verify the ESP32 is within range of the WiFi network
- Monitor Serial output for connection status
- Try increasing WIFI_MAX_RETRIES or WIFI_RETRY_DELAY

### Sensor Reading Problems
- Verify all connections according to the wiring diagram
- Check voltage divider resistor values match VOLTAGE_R1 and VOLTAGE_R2
- Adjust CURRENT_ZERO_POINT if current readings are offset
- Recalibrate LDR_RAW_ADC_MIN and LDR_RAW_ADC_MAX for your lighting conditions

### Communication Failures
- Verify SERVER_HOST and SERVER_PORT are correct
- Check that API_BASE_PATH and endpoint paths match your server configuration
- Ensure the server is running and accessible from the ESP32's network
- Try increasing MAX_HTTP_RETRIES if network is unreliable

### Control Issues
- Check relay connections and operation
- Verify NIGHT_HOUR_START and NIGHT_HOUR_END match your requirements
- Adjust LIGHT_PERCENT_DARK_THRESHOLD if light-based control is not working as expected
- Test manual override functionality


## Version History
- 1.0.4 (Current)
  - Improved fault detection logic
  - Added NTP time synchronization
  - Enhanced error handling and retry logic
  - Updated API integration

- 1.0.3
  - Added light-based control with LDR sensor
  - Implemented manual override functionality
  - Improved sensor reading accuracy

- 1.0.2
  - Added current and power monitoring
  - Implemented basic fault detection
  - Added status LED feedback

- 1.0.1
  - Added time-based control
  - Implemented relay control
  - Basic sensor readings

- 1.0.0
  - Initial release with basic functionality