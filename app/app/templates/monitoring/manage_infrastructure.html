{% extends 'base.html' %}

{% block title %}Manage Infrastructure - Tower Light Monitoring{% endblock %}

{% block extra_head %}
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.7.1/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.7.1/dist/leaflet.js"></script>
    <style>
        .leaflet-container {
            height: 400px;
            width: 100%;
        }
    </style>
{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8 animate-fade-in">
        <h1 class="text-3xl font-bold text-white mb-8">Manage Infrastructure</h1>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div class="bg-surface rounded-xl p-6 shadow-lg">
                <h2 class="text-2xl font-semibold text-white mb-4">Tower Lights</h2>
                <div class="space-y-4">
                    {% for tower in towers %}
                        <div class="bg-accent rounded-lg p-4 hover:bg-accent-light transition-colors">
                            <h3 class="text-lg font-medium text-white">{{ tower.name }}</h3>
                            <p class="text-gray-300">Status: {{ tower.status }}</p>
                            <button class="mt-2 bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-lg transition duration-200" onclick="showTowerDetails({{ tower.id }})">
                                Edit Details
                            </button>
                        </div>
                    {% endfor %}
                </div>
            </div>

            <div class="bg-surface rounded-xl p-6 shadow-lg">
                <h2 class="text-2xl font-semibold text-white mb-4">Map View</h2>
                <div id="map" class="rounded-lg"></div>
            </div>
        </div>

        <!-- Tower Details Modal -->
        <div id="towerModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center">
            <div class="bg-surface rounded-xl p-6 max-w-md w-full">
                <h2 id="modalTitle" class="text-2xl font-semibold text-white mb-4"></h2>
                <form id="towerForm" class="space-y-4">
                    <div>
                        <label for="towerName" class="block text-sm font-medium text-gray-300">Name</label>
                        <input type="text" id="towerName" name="name" class="mt-1 block w-full rounded-md bg-accent border-transparent focus:border-primary focus:bg-accent focus:ring-0 text-white">
                    </div>
                    <div>
                        <label for="towerStatus" class="block text-sm font-medium text-gray-300">Status</label>
                        <select id="towerStatus" name="status" class="mt-1 block w-full rounded-md bg-accent border-transparent focus:border-primary focus:bg-accent focus:ring-0 text-white">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="faulty">Faulty</option>
                            <option value="maintenance">Under Maintenance</option>
                        </select>
                    </div>
                    <div>
                        <label for="towerLatitude" class="block text-sm font-medium text-gray-300">Latitude</label>
                        <input type="number" id="towerLatitude" name="latitude" step="0.000001" class="mt-1 block w-full rounded-md bg-accent border-transparent focus:border-primary focus:bg-accent focus:ring-0 text-white">
                    </div>
                    <div>
                        <label for="towerLongitude" class="block text-sm font-medium text-gray-300">Longitude</label>
                        <input type="number" id="towerLongitude" name="longitude" step="0.000001" class="mt-1 block w-full rounded-md bg-accent border-transparent focus:border-primary focus:bg-accent focus:ring-0 text-white">
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button type="button" onclick="closeTowerModal()" class="bg-accent hover:bg-accent-light text-white font-bold py-2 px-4 rounded-lg transition duration-200">
                            Cancel
                        </button>
                        <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded-lg transition duration-200">
                            Save Changes
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script>
        let map;
        let markers = {};

        document.addEventListener('DOMContentLoaded', function() {
            // Initialize map
            map = L.map('map').setView([-20.1560, 28.5885], 13);
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors'
            }).addTo(map);

            // Fetch and display tower locations
            fetch('/api/tower-locations/')
                .then(response => response.json())
                .then(data => {
                    data.towers.forEach(tower => {
                        addTowerMarker(tower);
                    });
                });
        });

        function addTowerMarker(tower) {
            const color = tower.status === 'active' ? '#3D8FCA' :
                tower.status === 'faulty' ? '#FF574D' : '#FFC85C';

            const marker = L.circleMarker([tower.lat, tower.lng], {
                radius: 8,
                fillColor: color,
                color: '#fff',
                weight: 2,
                opacity: 1,
                fillOpacity: 0.8
            }).addTo(map);

            marker.bindPopup(`
            <b>${tower.name}</b><br>
            Status: ${tower.status}<br>
            <button onclick="showTowerDetails(${tower.id})" class="text-primary hover:text-primary-dark">Edit Details</button>
        `);

            markers[tower.id] = marker;
        }

        function showTowerDetails(towerId) {
            fetch(`/api/towers/${towerId}/`)
                .then(response => response.json())
                .then(tower => {
                    document.getElementById('modalTitle').textContent = `Edit ${tower.name}`;
                    document.getElementById('towerName').value = tower.name;
                    document.getElementById('towerStatus').value = tower.status;
                    document.getElementById('towerLatitude').value = tower.latitude;
                    document.getElementById('towerLongitude').value = tower.longitude;
                    document.getElementById('towerForm').onsubmit = (e) => updateTower(e, towerId);
                    document.getElementById('towerModal').classList.remove('hidden');
                    document.getElementById('towerModal').classList.add('flex');
                });
        }

        function closeTowerModal() {
            document.getElementById('towerModal').classList.add('hidden');
            document.getElementById('towerModal').classList.remove('flex');
        }

        function updateTower(event, towerId) {
            event.preventDefault();

            const formData = new FormData(event.target);
            const updatedTower = {
                name: formData.get('name'),
                status: formData.get('status'),
                latitude: parseFloat(formData.get('latitude')),
                longitude: parseFloat(formData.get('longitude'))
            };

            fetch(`/api/towers/${towerId}/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(updatedTower)
            })
            .then(response => {
                if (!response.ok) {
                    return response.json().then(data => {
                        throw new Error(JSON.stringify(data));
                    });
                }
                return response.json();
            })
            .then(tower => {
                // Update success handling
                const marker = markers[tower.id];
                if (marker) {
                    const color = tower.status === 'active' ? '#3D8FCA' :
                                tower.status === 'faulty' ? '#FF574D' : '#FFC85C';

                    marker.setLatLng([tower.latitude, tower.longitude]);
                    marker.setStyle({fillColor: color});
                }

                closeTowerModal();
                alert('Tower updated successfully!');
                location.reload();
            })
            .catch(error => {
                console.error('Error updating tower:', error);
                try {
                    const errorData = JSON.parse(error.message);
                    const errorMessage = Object.entries(errorData)
                        .map(([key, value]) => `${key}: ${value}`)
                        .join('\n');
                    alert(`Error updating tower:\n${errorMessage}`);
                } catch {
                    alert('Error updating tower. Please try again.');
                }
            });
        }

        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
    </script>
{% endblock %}

