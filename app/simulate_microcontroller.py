"""
Microcontroller Simulation Script for Tower Light Monitoring System

This script simulates microcontroller data for tower lights 1 and 2:
1. Sends realistic sensor data to the API endpoint
2. Simulates different scenarios (normal operation, faults, etc.)
3. Can run continuously to provide a stream of data

Usage:
  python simulate_microcontroller.py [options]

Options:
  --continuous    Run continuously, sending data every 10 seconds
  --fault         Simulate a fault condition
  --tower=ID      Specify which tower to simulate (1 or 2, default: both)
  --help          Show this help message
"""

import os
import sys
import django
import requests
import json
import random
import time
import argparse
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

# Import models after Django setup
from monitoring.models import TowerLight, SensorData

# API endpoint for sending sensor data from microcontrollers
API_ENDPOINT = "http://localhost:8000/api/tower-status-update/"

def generate_sensor_data(tower_id, is_on=True, is_faulty=False, fault_type=None):
    """Generate realistic sensor data for a tower light"""
    
    # Base values
    if is_on:
        # When light is ON: voltage between 11.6V and 12.5V, current between 0.3A and 2A
        voltage = round(random.uniform(11.6, 12.5), 1)
        current = round(random.uniform(0.3, 0.8), 2)
        light_level = round(random.uniform(90, 100), 0)
        efficiency = round(random.uniform(85, 95), 1)
    else:
        # When light is OFF: voltage between 0V and 0.5V, current between 0A and 0.2A
        voltage = round(random.uniform(0.0, 0.5), 1)
        current = round(random.uniform(0.0, 0.2), 2)
        light_level = round(random.uniform(10, 20), 0)
        efficiency = 0
    
    # Calculate power
    power = round(voltage * current, 2)
    
    # Add fault data if needed
    faulty_lights = 1 if is_faulty else 0
    maintenance_lights = 0
    
    # Modify values for fault conditions
    if is_faulty:
        if fault_type == 'power':
            # Power fault: abnormal voltage/current
            if random.choice([True, False]):
                # High voltage
                voltage = round(random.uniform(13.0, 15.0), 1)
            else:
                # Low voltage
                voltage = round(random.uniform(8.0, 10.0), 1)
            current = round(random.uniform(0.1, 0.3), 2)
            efficiency = round(random.uniform(40, 60), 1)
        elif fault_type == 'bulb':
            # Bulb fault: low light level despite normal power
            light_level = round(random.uniform(30, 50), 0)
            efficiency = round(random.uniform(30, 50), 1)
        elif fault_type == 'connection':
            # Connection fault: intermittent readings
            if random.choice([True, False]):
                voltage = None
                current = None
                power = None
            efficiency = round(random.uniform(20, 40), 1)
    
    # Generate sensor data
    data = {
        "tower_id": tower_id,
        "sensor_value": random.randint(2000, 4000),
        "light_level": light_level,
        "voltage": voltage,
        "current": current,
        "power": power,
        "efficiency": efficiency,
        "faulty_lights": faulty_lights,
        "maintenance_lights": maintenance_lights
    }
    
    # Add fault information if applicable
    if is_faulty and fault_type:
        data["status"] = "faulty"
        data["fault_type"] = fault_type
    
    return data

def send_sensor_data(data):
    """Send sensor data to the API endpoint"""
    try:
        # Set the correct headers for the request
        headers = {
            'Content-Type': 'application/json',
        }
        
        # Make the POST request
        response = requests.post(API_ENDPOINT, json=data, headers=headers)
        
        if response.status_code == 200:
            print(f"✅ Successfully sent data for Tower {data['tower_id']}")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Failed to send data for Tower {data['tower_id']}")
            print(f"   Status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error sending data: {e}")
        return False

def verify_data_received(tower_id):
    """Verify that the data was received and stored correctly"""
    try:
        # Get the latest sensor data for the tower
        latest_data = SensorData.objects.filter(tower_id=tower_id).order_by('-timestamp').first()
        
        if latest_data:
            print(f"\nLatest data for Tower {tower_id}:")
            print(f"  Timestamp: {latest_data.timestamp}")
            print(f"  Voltage: {latest_data.voltage}V")
            print(f"  Current: {latest_data.current}A")
            print(f"  Power: {latest_data.power}W")
            print(f"  Light Level: {latest_data.light_level}%")
            print(f"  Efficiency: {latest_data.efficiency}%")
            print(f"  Faulty Lights: {latest_data.faulty_lights}")
            print(f"  Maintenance Lights: {latest_data.maintenance_lights}")
            return True
        else:
            print(f"❌ No data found for Tower {tower_id}")
            return False
    except Exception as e:
        print(f"❌ Error verifying data: {e}")
        return False

def simulate_microcontroller(tower_id=None, continuous=False, fault=False, interval=10):
    """Simulate sending data from microcontrollers to tower lights"""
    try:
        print("\n=== Tower Light Microcontroller Simulation ===\n")
        
        # Check if Django server is running
        try:
            response = requests.get("http://localhost:8000/api/tower-data/")
            if response.status_code != 200:
                print("❌ Django server is not running or not responding correctly")
                print("Please start the server with: python manage.py runserver")
                return
        except requests.exceptions.ConnectionError:
            print("❌ Django server is not running")
            print("Please start the server with: python manage.py runserver")
            return
        
        # Determine which towers to simulate
        towers_to_simulate = [1, 2] if tower_id is None else [int(tower_id)]
        
        # Run once or continuously
        iteration = 1
        while True:
            # Get current time to determine if it's night time
            current_time = datetime.now()
            current_hour = current_time.hour
            is_nighttime = (current_hour >= 18 or current_hour < 6)
            
            print(f"\nIteration {iteration} - {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print(f"Is nighttime: {is_nighttime}")
            
            # Simulate data for each tower
            for tower_id in towers_to_simulate:
                # Determine if this tower should have a fault
                is_faulty = fault
                fault_type = None
                
                if is_faulty:
                    # Randomly choose a fault type
                    fault_type = random.choice(['power', 'bulb', 'connection'])
                    print(f"\nSimulating {fault_type} fault for Tower {tower_id}...")
                else:
                    print(f"\nSimulating normal operation for Tower {tower_id}...")
                
                # Generate and send data
                tower_data = generate_sensor_data(
                    tower_id=tower_id,
                    is_on=is_nighttime,
                    is_faulty=is_faulty,
                    fault_type=fault_type
                )
                send_sensor_data(tower_data)
                verify_data_received(tower_id)
            
            # Break if not running continuously
            if not continuous:
                break
            
            # Wait before sending next batch of data
            print(f"\nWaiting {interval} seconds before next update...")
            time.sleep(interval)
            iteration += 1
        
        print("\nSimulation complete!")
        print("Tower lights should now display the real data from the microcontrollers")
        print("Refresh the tower lights page to see the updated data")
        
    except KeyboardInterrupt:
        print("\nSimulation stopped by user")
    except Exception as e:
        print(f"\n❌ Error during simulation: {e}")

if __name__ == "__main__":
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Simulate microcontroller data for tower lights')
    parser.add_argument('--continuous', action='store_true', help='Run continuously, sending data every 10 seconds')
    parser.add_argument('--fault', action='store_true', help='Simulate a fault condition')
    parser.add_argument('--tower', type=int, choices=[1, 2], help='Specify which tower to simulate (1 or 2, default: both)')
    parser.add_argument('--interval', type=int, default=10, help='Interval between updates in seconds (default: 10)')
    
    args = parser.parse_args()
    
    # Run the simulation
    simulate_microcontroller(
        tower_id=args.tower,
        continuous=args.continuous,
        fault=args.fault,
        interval=args.interval
    )
