"""
Database Cleanup Script for Tower Light Monitoring System

This script cleans the database by:
1. Removing all fault logs
2. Removing all sensor data
3. Resetting tower lights to their default states
4. Ensuring tower lights 1 and 2 are ready to receive data from microcontrollers
5. Ensuring tower lights 3-20 are set to 'active' status

Run this script when you want to start fresh with a clean database.
"""

import os
import sys
import django
from django.utils import timezone
from django.db import connection

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

# Import models after Django setup
from monitoring.models import TowerLight, FaultLog
from monitoring.models import SensorData

def clean_database():
    """Clean the database of operational data"""
    try:
        print("\n=== Tower Light Monitoring System Database Cleanup ===\n")
        
        # 1. Remove all fault logs
        fault_count = FaultLog.objects.count()
        FaultLog.objects.all().delete()
        print(f"✅ Removed {fault_count} fault logs")
        
        # 2. Remove all sensor data
        sensor_count = SensorData.objects.count()
        SensorData.objects.all().delete()
        print(f"✅ Removed {sensor_count} sensor data records")
        
        # 3. Get current tower count
        tower_count = TowerLight.objects.count()
        print(f"Found {tower_count} tower lights in the database")
        
        # 4. Reset tower lights to default states
        current_date = timezone.now().date()
        
        # 4a. Reset tower lights 1 and 2 (microcontroller towers)
        TowerLight.objects.filter(id=1).update(
            name='Tower 1 (Microcontroller)',
            status='active',
            description='Tower with active microcontroller - receives real-time data'
        )
        print("✅ Reset Tower 1 to default state")
        
        TowerLight.objects.filter(id=2).update(
            name='Tower 2 (Microcontroller)',
            status='active',
            description='Tower with active microcontroller - receives real-time data'
        )
        print("✅ Reset Tower 2 to default state")
        
        # 4b. Reset tower lights 3-20 (simulated towers)
        updated = TowerLight.objects.filter(id__range=(3, 20)).update(
            status='active',
            description='Simulated tower - follows scheduled operation'
        )
        print(f"✅ Reset {updated} simulated towers (3-20) to 'active' status")
        
        # 5. Verify the changes
        print("\nVerifying database state:")
        
        # 5a. Check fault logs
        if FaultLog.objects.count() == 0:
            print("✅ No fault logs in database")
        else:
            print(f"⚠️ {FaultLog.objects.count()} fault logs still exist")
        
        # 5b. Check sensor data
        if SensorData.objects.count() == 0:
            print("✅ No sensor data in database")
        else:
            print(f"⚠️ {SensorData.objects.count()} sensor data records still exist")
        
        # 5c. Check tower lights
        towers = TowerLight.objects.all().order_by('id')
        print("\nCurrent Tower Status:")
        for tower in towers:
            if tower.id <= 2:
                if tower.status == 'active':
                    print(f"✅ Tower {tower.id}: {tower.status} (Ready for microcontroller data)")
                else:
                    print(f"⚠️ Tower {tower.id}: {tower.status} (Should be 'active')")
            elif 3 <= tower.id <= 20:
                if tower.status == 'active':
                    print(f"✅ Tower {tower.id}: {tower.status} (Simulated tower)")
                else:
                    print(f"⚠️ Tower {tower.id}: {tower.status} (Should be 'active')")
        
        # 6. Vacuum the database to reclaim space (SQLite only)
        try:
            if connection.vendor == 'sqlite':
                cursor = connection.cursor()
                cursor.execute("VACUUM;")
                print("\n✅ Database vacuumed to reclaim space")
        except Exception as e:
            print(f"⚠️ Could not vacuum database: {e}")
        
        print("\n=== Database Cleanup Complete ===")
        print("\nYour database is now clean and ready for operation.")
        print("Tower lights 1 and 2 are ready to receive data from microcontrollers.")
        print("Tower lights 3-20 are set to 'active' status and will follow the scheduled operation.")
        
    except Exception as e:
        print(f"\n❌ Error during database cleanup: {e}")
        print("Database may be in an inconsistent state.")

if __name__ == "__main__":
    # Ask for confirmation before proceeding
    print("WARNING: This will delete all operational data from the database.")
    print("All fault logs and sensor data will be permanently removed.")
    print("Tower lights will be reset to their default states.")
    confirmation = input("Are you sure you want to proceed? (yes/no): ")
    
    if confirmation.lower() in ['yes', 'y']:
        clean_database()
    else:
        print("Database cleanup cancelled.")
