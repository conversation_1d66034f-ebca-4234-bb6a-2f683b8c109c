{% extends 'base.html' %}

{% block title %}Control Panel - Tower Light Monitoring{% endblock %}

{% block content %}
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-white mb-8">Control Panel</h1>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {% for tower in towers %}
                <div class="bg-surface rounded-xl shadow-lg p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{{ tower.name }}</h2>
                    <p class="mb-2 text-gray-300">Status: <span class="font-medium text-white">{{ tower.get_status_display }}</span></p>
                    <p class="mb-4 text-gray-300">Last checked: {{ tower.last_checked|date:"F j, Y, g:i a" }}</p>

                    <div class="flex space-x-2">
                        <button class="bg-primary hover:bg-primary-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'turn_on')">
                            Turn On
                        </button>
                        <button class="bg-secondary hover:bg-secondary-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'turn_off')">
                            Turn Off
                        </button>
                        <button class="bg-warning hover:bg-warning-dark text-white font-bold py-2 px-4 rounded transition duration-300 ease-in-out flex-1"
                                onclick="controlTower('{{ tower.id }}', 'maintenance')">
                            Maintenance
                        </button>
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <script>
        function controlTower(towerId, action) {
            fetch(`/api/towers/${towerId}/control/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify({ action: action })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Update the UI or show a success message
                        alert(`Tower ${towerId} ${action} successful`);
                        // Refresh the page to show updated status
                        location.reload();
                    } else {
                        alert(`Error: ${data.message}`);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred. Please try again.');
                });
        }
    </script>
{% endblock %}

