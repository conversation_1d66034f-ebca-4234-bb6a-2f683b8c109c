"""
Test script to simulate sending data from microcontrollers to tower lights 1 and 2.

This script:
1. Sends simulated sensor data to the API endpoint for tower lights 1 and 2
2. Verifies that the data is received and stored correctly
3. Demonstrates how microcontroller data updates the tower light status
"""

import os
import sys
import django
import requests
import json
import random
import time
from datetime import datetime

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

# Import models after Django setup
from monitoring.models import TowerLight, SensorData

# API endpoint for sending sensor data from microcontrollers
API_ENDPOINT = "http://localhost:8000/api/tower-status-update/"

def generate_sensor_data(tower_id, is_on=True, is_faulty=False):
    """Generate realistic sensor data for a tower light"""

    # Base values
    if is_on:
        # When light is ON: voltage between 11.6V and 12.5V, current between 0.3A and 2A
        voltage = round(random.uniform(11.6, 12.5), 1)
        current = round(random.uniform(0.3, 0.8), 2)
        light_level = round(random.uniform(90, 100), 0)
    else:
        # When light is OFF: voltage between 0V and 0.5V, current between 0A and 0.2A
        voltage = round(random.uniform(0.0, 0.5), 1)
        current = round(random.uniform(0.0, 0.2), 2)
        light_level = round(random.uniform(10, 20), 0)

    # Calculate power
    power = round(voltage * current, 2)

    # Calculate efficiency
    if is_on:
        efficiency = round(random.uniform(85, 95), 1)
    else:
        efficiency = 0

    # Add fault data if needed
    faulty_lights = 1 if is_faulty else 0
    maintenance_lights = 0

    # Generate sensor data
    data = {
        "tower_id": tower_id,
        "sensor_value": random.randint(2000, 4000),
        "light_level": light_level,
        "voltage": voltage,
        "current": current,
        "power": power,
        "efficiency": efficiency,
        "faulty_lights": faulty_lights,
        "maintenance_lights": maintenance_lights
    }

    return data

def send_sensor_data(data):
    """Send sensor data to the API endpoint"""
    try:
        # Set the correct headers for the request
        headers = {
            'Content-Type': 'application/json',
        }

        # Make the POST request
        response = requests.post(API_ENDPOINT, json=data, headers=headers)

        if response.status_code == 200:
            print(f"✅ Successfully sent data for Tower {data['tower_id']}")
            print(f"   Response: {response.json()}")
            return True
        else:
            print(f"❌ Failed to send data for Tower {data['tower_id']}")
            print(f"   Status code: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error sending data: {e}")
        return False

def verify_data_received(tower_id):
    """Verify that the data was received and stored correctly"""
    try:
        # Get the latest sensor data for the tower
        latest_data = SensorData.objects.filter(tower_id=tower_id).order_by('-timestamp').first()

        if latest_data:
            print(f"\nLatest data for Tower {tower_id}:")
            print(f"  Timestamp: {latest_data.timestamp}")
            print(f"  Voltage: {latest_data.voltage}V")
            print(f"  Current: {latest_data.current}A")
            print(f"  Power: {latest_data.power}W")
            print(f"  Light Level: {latest_data.light_level}%")
            print(f"  Efficiency: {latest_data.efficiency}%")
            print(f"  Faulty Lights: {latest_data.faulty_lights}")
            print(f"  Maintenance Lights: {latest_data.maintenance_lights}")
            return True
        else:
            print(f"❌ No data found for Tower {tower_id}")
            return False
    except Exception as e:
        print(f"❌ Error verifying data: {e}")
        return False

def simulate_microcontroller_data():
    """Simulate sending data from microcontrollers to tower lights 1 and 2"""
    try:
        print("\n=== Tower Light Microcontroller Simulation ===\n")

        # Check if Django server is running
        try:
            response = requests.get("http://localhost:8000/api/tower-data/")
            if response.status_code != 200:
                print("❌ Django server is not running or not responding correctly")
                print("Please start the server with: python manage.py runserver")
                return
        except requests.exceptions.ConnectionError:
            print("❌ Django server is not running")
            print("Please start the server with: python manage.py runserver")
            return

        # Get current time to determine if it's night time
        current_hour = datetime.now().hour
        is_nighttime = (current_hour >= 18 or current_hour < 6)

        print(f"Current time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Is nighttime: {is_nighttime}")

        # Simulate data for Tower 1 (normal operation)
        print("\nSending data for Tower 1 (normal operation)...")
        tower1_data = generate_sensor_data(tower_id=1, is_on=is_nighttime, is_faulty=False)
        send_sensor_data(tower1_data)
        verify_data_received(1)

        # Simulate data for Tower 2 (with fault)
        print("\nSending data for Tower 2 (with fault)...")
        tower2_data = generate_sensor_data(tower_id=2, is_on=is_nighttime, is_faulty=True)
        send_sensor_data(tower2_data)
        verify_data_received(2)

        print("\nSimulation complete!")
        print("Tower lights 1 and 2 should now display the real data from the microcontrollers")
        print("Refresh the tower lights page to see the updated data")

    except Exception as e:
        print(f"❌ Error during simulation: {e}")

if __name__ == "__main__":
    simulate_microcontroller_data()
