"""
Test script to verify that tower lights 3-20 have the correct voltage, current, and light intensity values
based on their status and time of day.
"""

import os
import sys
import django
from django.utils import timezone
import requests
import json
import time

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

# Import models after Django setup
from monitoring.models import TowerLight, SensorData
from django.utils import timezone

def verify_tower_thresholds():
    """Verify that tower lights 3-20 have the correct voltage, current, and light intensity values"""
    try:
        # Get current time to determine if it's night time
        current_time = timezone.now()
        current_hour = current_time.hour
        is_nighttime = (current_hour >= 18 or current_hour < 6)
        
        print("\nTower Thresholds Verification:")
        print("-----------------------------")
        print(f"Current time: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"Is nighttime: {is_nighttime}")
        
        print("\nExpected Thresholds:")
        print("- When light is ON (nighttime): voltage 11.0V-12.4V, current 0.2A-2.0A, light intensity 75-100%")
        print("- When light is OFF (daytime): voltage 0V-0.5V, current 0A-0.2A, light intensity 10-20%")
        
        # Make a request to the API to get the actual values
        print("\nFetching actual values from API...")
        
        # Start Django server if not already running
        print("Please make sure your Django server is running on http://localhost:8000")
        
        try:
            # Try to fetch data from the API
            response = requests.get('http://localhost:8000/api/tower-data/')
            if response.status_code == 200:
                data = response.json()
                
                print("\nActual Tower Values:")
                print("--------------------")
                
                # Check towers 3-20
                for tower in data['towers']:
                    if 3 <= tower['id'] <= 20:
                        status = tower['status']
                        voltage = tower['sensor_data']['voltage']
                        current = tower['sensor_data']['current']
                        power = tower['sensor_data']['power']
                        light_level = tower['sensor_data']['light_level']
                        
                        # Determine expected ranges based on time of day
                        if is_nighttime and status == 'active':
                            # When light is ON
                            expected_voltage_range = "11.0V-12.4V"
                            expected_current_range = "0.2A-2.0A"
                            expected_light_range = "75-100%"
                            is_valid_voltage = 11.0 <= voltage <= 12.4
                            is_valid_current = 0.2 <= current <= 2.0
                            is_valid_light = 75 <= light_level <= 100
                        else:
                            # When light is OFF
                            expected_voltage_range = "0V-0.5V"
                            expected_current_range = "0A-0.2A"
                            expected_light_range = "10-20%"
                            is_valid_voltage = 0.0 <= voltage <= 0.5
                            is_valid_current = 0.0 <= current <= 0.2
                            is_valid_light = 10 <= light_level <= 20
                        
                        # Check if power is calculated correctly
                        calculated_power = round(voltage * current, 2)
                        is_power_correct = abs(power - calculated_power) < 0.1  # Allow for small rounding differences
                        
                        print(f"Tower {tower['id']}: Status={status}, " +
                              f"Voltage={voltage}V (Expected: {expected_voltage_range}), " +
                              f"Current={current}A (Expected: {expected_current_range}), " +
                              f"Light={light_level}% (Expected: {expected_light_range}), " +
                              f"Power={power}W (Calculated: {calculated_power}W)")
                        
                        if not is_valid_voltage:
                            print(f"  ❌ Voltage is outside expected range!")
                        else:
                            print(f"  ✅ Voltage is within expected range")
                            
                        if not is_valid_current:
                            print(f"  ❌ Current is outside expected range!")
                        else:
                            print(f"  ✅ Current is within expected range")
                            
                        if not is_valid_light:
                            print(f"  ❌ Light intensity is outside expected range!")
                        else:
                            print(f"  ✅ Light intensity is within expected range")
                            
                        if not is_power_correct:
                            print(f"  ❌ Power is not calculated correctly!")
                        else:
                            print(f"  ✅ Power is calculated correctly")
                
                print("\nVerification Complete!")
                print("If all values are within expected ranges, the fix was successful.")
                
            else:
                print(f"Error: API returned status code {response.status_code}")
                print("Please make sure your Django server is running on http://localhost:8000")
                
        except requests.exceptions.ConnectionError:
            print("Error: Could not connect to the Django server")
            print("Please make sure your Django server is running on http://localhost:8000")
            print("\nTo run the server, open a new terminal and run:")
            print("python manage.py runserver")
            
    except Exception as e:
        print(f"Error during verification: {e}")

if __name__ == "__main__":
    verify_tower_thresholds()
