from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator, EmptyPage, PageNotAnInteger
from django.http import JsonResponse
from django.shortcuts import redirect, get_object_or_404
from django.utils import timezone
from django.utils.decorators import method_decorator
from django.views.generic import TemplateView, ListView, DetailView
from rest_framework.views import APIView
from rest_framework.response import Response
from django.views.decorators.csrf import csrf_exempt
from .models import TowerLight, FaultLog, MaintenanceSchedule, SensorData
from rest_framework import status


@method_decorator(login_required, name='dispatch')
class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'monitoring/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        # Get counts for status cards
        status_counts = TowerLight.get_status_counts()
        status_dict = {item['status']: item['count'] for item in status_counts}

        # Calculate efficiency
        efficiency = TowerLight.calculate_efficiency()

        # Get recent alerts
        recent_alerts = FaultLog.get_recent_alerts()

        # Get upcoming maintenance
        upcoming_maintenance = MaintenanceSchedule.get_upcoming_maintenance()

        # Calculate weekly changes
        fault_change = FaultLog.calculate_weekly_change()
        maintenance_change = MaintenanceSchedule.calculate_weekly_change()

        context.update({
            'active_count': status_dict.get('active', 0),
            'faulty_count': status_dict.get('faulty', 0),
            'maintenance_count': status_dict.get('maintenance', 0),
            'efficiency': round(efficiency, 1),
            'recent_alerts': recent_alerts,
            'upcoming_maintenance': upcoming_maintenance,
            'fault_change': round(fault_change, 1),
            'maintenance_change': round(maintenance_change, 1),
            'current_time': timezone.now(),
        })

        return context

class AlertListView(LoginRequiredMixin, ListView):
    model = FaultLog
    template_name = 'monitoring/alerts.html'
    context_object_name = 'alerts'
    paginate_by = 20

    def get_queryset(self):
        queryset = super().get_queryset()

        status = self.request.GET.get('status')
        if status:
            if status == 'active':
                queryset = queryset.filter(resolved=False)
            elif status == 'resolved':
                queryset = queryset.filter(resolved=True)

        start_date = self.request.GET.get('start_date')
        end_date = self.request.GET.get('end_date')
        if start_date and end_date:
            queryset = queryset.filter(timestamp__range=[start_date, end_date])
        elif start_date:
            queryset = queryset.filter(timestamp__gte=start_date)
        elif end_date:
            queryset = queryset.filter(timestamp__lte=end_date)

        return queryset.select_related('tower', 'resolved_by')

class TowerLightDetailView(LoginRequiredMixin, DetailView):
    model = TowerLight
    template_name = 'monitoring/tower_detail.html'
    context_object_name = 'tower'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['fault_history'] = self.object.faults.all()[:10]
        context['maintenance_schedule'] = self.object.maintenance_schedules.filter(
            completed=False
        )
        return context

class TowerLocationsView(LoginRequiredMixin, TemplateView):
    def get(self, request, *args, **kwargs):
        towers = TowerLight.objects.all()
        tower_data = [{
            'id': tower.id,
            'name': tower.name,
            'lat': tower.latitude,
            'lng': tower.longitude,
            'status': tower.status
        } for tower in towers]

        return JsonResponse({'towers': tower_data})

class LandingPageView(TemplateView):
    template_name = 'landing_page.html'

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            return redirect('monitoring:dashboard')
        return super().dispatch(request, *args, **kwargs)


@method_decorator(login_required, name='dispatch')
class ControlPanelView(LoginRequiredMixin, TemplateView):
    template_name = 'monitoring/control_panel.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['towers'] = TowerLight.objects.all()
        return context

    def post(self, request, *args, **kwargs):
        tower_id = request.POST.get('tower_id')
        action = request.POST.get('action')
        tower = get_object_or_404(TowerLight, id=tower_id)

        if action == 'turn_on':
            tower.status = 'active'
        elif action == 'turn_off':
            tower.status = 'inactive'
        elif action == 'maintenance':
            tower.status = 'maintenance'

        tower.save()

        return JsonResponse({'status': 'success', 'new_status': tower.status})

class ManageInfrastructureView(LoginRequiredMixin, TemplateView):
    template_name = 'monitoring/manage_infrastructure.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['towers'] = TowerLight.objects.all()
        return context

class AlertDetailView(LoginRequiredMixin, DetailView):
    model = FaultLog
    template_name = 'monitoring/alert_detail.html'
    context_object_name = 'alert'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['tower'] = self.object.tower
        context['related_faults'] = FaultLog.objects.filter(tower=self.object.tower).exclude(id=self.object.id)[:5]
        return context

class TowerLightsView(TemplateView):
    template_name = 'monitoring/tower_lights.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # Get all tower lights with pagination
        page = self.request.GET.get('page', 1)
        towers_per_page = 9  # Number of tower cards per page

        # Ensure we have at least 12 towers
        towers = TowerLight.objects.all().order_by('id')
        if not towers.exists() or towers.count() < 12:
            # Create 12 sample towers for demonstration
            current_date = timezone.now().date()
            sample_towers = [
                {
                    'name': 'Tower 1 (Active Sensor)',
                    'latitude': -17.8252,
                    'longitude': 31.0335,
                    'status': 'active',
                    'installation_date': current_date
                },
                {
                    'name': 'Tower 2 (Active Sensor)',
                    'latitude': -17.8350,
                    'longitude': 31.0450,
                    'status': 'active',
                    'installation_date': current_date
                },
            ]

            # Add 18 more regular towers around Harare, Zimbabwe
            for i in range(3, 21):
                lat_offset = (i % 5) * 0.01 - 0.02
                lng_offset = (i // 5) * 0.01 - 0.02

                # Set all towers 3-20 to active status
                # As per requirement, tower lights 3-20 should always be active
                status = 'active'

                sample_towers.append({
                    'name': f'Tower {i}',
                    'latitude': -17.8252 + lat_offset,
                    'longitude': 31.0335 + lng_offset,
                    'status': status,
                    'installation_date': current_date
                })

            # Create or update towers
            for i, tower_data in enumerate(sample_towers):
                TowerLight.objects.update_or_create(
                    id=i+1,
                    defaults={
                        'name': tower_data['name'],
                        'latitude': tower_data['latitude'],
                        'longitude': tower_data['longitude'],
                        'status': tower_data['status'],
                        'installation_date': tower_data['installation_date']
                    }
                )

            # Refresh the queryset
            towers = TowerLight.objects.all().order_by('id')

        paginator = Paginator(towers, towers_per_page)

        try:
            towers_page = paginator.page(page)
        except (PageNotAnInteger, EmptyPage):
            towers_page = paginator.page(1)

        context['towers'] = towers_page
        context['current_time'] = timezone.now()
        return context

class TowerDataAPIView(APIView):
    """API endpoint for tower data"""
    permission_classes = []  # Allow unauthenticated access

    def get(self, request):
        try:
            page = request.GET.get('page', 1)
            towers_per_page = 9

            # Get all towers with ordering
            towers = TowerLight.objects.all().order_by('id')

            # Debug log
            print(f"Found {towers.count()} towers")

            # Create sample towers if none exist
            if not towers.exists() or towers.count() < 12:
                current_date = timezone.now().date()
                sample_towers = [
                    {
                        'name': 'Tower 1 (Active Sensor)',
                        'latitude': -17.8252,
                        'longitude': 31.0335,
                        'status': 'active',
                        'installation_date': current_date
                    },
                    {
                        'name': 'Tower 2 (Active Sensor)',
                        'latitude': -17.8350,
                        'longitude': 31.0450,
                        'status': 'active',
                        'installation_date': current_date
                    },
                ]

                # Add 18 more regular towers around Harare, Zimbabwe
                for i in range(3, 21):
                    lat_offset = (i % 5) * 0.01 - 0.02
                    lng_offset = (i // 5) * 0.01 - 0.02

                    # Set all towers 3-20 to active status
                    # As per requirement, tower lights 3-20 should always be active
                    status = 'active'

                    sample_towers.append({
                        'name': f'Tower {i}',
                        'latitude': -17.8252 + lat_offset,
                        'longitude': 31.0335 + lng_offset,
                        'status': status,
                        'installation_date': current_date
                    })

                # Create or update towers
                for i, tower_data in enumerate(sample_towers):
                    TowerLight.objects.update_or_create(
                        id=i+1,
                        defaults={
                            'name': tower_data['name'],
                            'latitude': tower_data['latitude'],
                            'longitude': tower_data['longitude'],
                            'status': tower_data['status'],
                            'installation_date': tower_data['installation_date']
                        }
                    )

                # Refresh the queryset
                towers = TowerLight.objects.all().order_by('id')

            paginator = Paginator(towers, towers_per_page)

            try:
                towers_page = paginator.page(page)
            except (PageNotAnInteger, EmptyPage):
                towers_page = paginator.page(1)

            # Check if it's nighttime (18:00-06:00 GMT+2)
            current_time = timezone.now()
            current_hour = current_time.hour
            # Nighttime: 18:00 to 05:59
            is_nighttime = (current_hour >= 18 or current_hour < 6)

            tower_data = []
            for tower in towers_page:
                # Get the latest sensor data for this tower
                latest_sensor_data = SensorData.objects.filter(tower=tower).order_by('-timestamp').first()

                tower_info = {
                    'id': tower.id,
                    'name': tower.name,
                    'status': tower.status,
                    'latitude': tower.latitude,
                    'longitude': tower.longitude,
                    'sensor_data': None
                }

                # If no real sensor data
                if not latest_sensor_data:
                    import random

                    # For towers 1 and 2, only use real data from microcontrollers
                    if tower.id <= 2:
                        # No data received from microcontroller yet - show null values
                        tower_info['sensor_data'] = {
                            'timestamp': current_time.isoformat(),
                            'sensor_value': None,
                            'light_level': None,
                            'faulty_lights': None,
                            'maintenance_lights': None,
                            'efficiency': None,
                            'voltage': None,
                            'current': None,
                            'power': None
                        }
                    else:
                        # For towers 3-20, generate simulated data
                        # Determine if the light should be ON or OFF based on time of day
                        # Lights are ON at night (6pm-6am) and OFF during the day (6am-6pm)
                        is_light_on = is_nighttime

                        # 'active' status means the tower is fully functional (not faulty)
                        # The light can be either ON or OFF based on time of day
                        if is_light_on and tower.status != 'faulty' and tower.status != 'maintenance':
                            # When light is ON: voltage between 11.0V and 12.4V, current between 0.2A and 2A
                            voltage = round(random.uniform(11.0, 12.4), 1)
                            current = round(random.uniform(0.2, 2.0), 2)
                            power = round(voltage * current, 2)
                            efficiency = round(random.uniform(85, 95), 1)
                            light_level = round(random.uniform(75, 100), 0)  # Light intensity between 75-100%
                        else:
                            # When light is OFF: voltage between 0V and 0.5V, current between 0A and 0.2A
                            # This applies to: daytime, faulty towers, and maintenance towers
                            voltage = round(random.uniform(0.0, 0.5), 1)
                            current = round(random.uniform(0.0, 0.2), 2)
                            power = round(voltage * current, 2)
                            efficiency = 0
                            light_level = round(random.uniform(10, 20), 0)

                        tower_info['sensor_data'] = {
                            'timestamp': current_time.isoformat(),
                            'sensor_value': random.randint(2000, 4000),
                            'light_level': light_level,
                            'faulty_lights': 1 if tower.status == 'faulty' else 0,
                            'maintenance_lights': 1 if tower.status == 'maintenance' else 0,
                            'efficiency': efficiency,
                            'voltage': voltage,
                            'current': current,
                            'power': power
                        }
                else:
                    # Use real sensor data
                    tower_info['sensor_data'] = {
                        'timestamp': latest_sensor_data.timestamp.isoformat(),
                        'sensor_value': latest_sensor_data.sensor_value,
                        'light_level': latest_sensor_data.light_level,
                        'faulty_lights': latest_sensor_data.faulty_lights,
                        'maintenance_lights': latest_sensor_data.maintenance_lights,
                        'efficiency': latest_sensor_data.efficiency,
                        'voltage': latest_sensor_data.voltage or 0,
                        'current': latest_sensor_data.current or 0,
                        'power': latest_sensor_data.power or 0
                    }

                tower_data.append(tower_info)
                print(f"Added tower {tower.id}: {tower.name} with {'real' if latest_sensor_data else 'simulated'} data")

            response_data = {
                'towers': tower_data,
                'pagination': {
                    'current_page': int(page),
                    'total_pages': paginator.num_pages,
                    'total_towers': towers.count(),
                    'towers_per_page': towers_per_page
                }
            }
            print("API Response:", response_data)
            return Response(response_data)
        except Exception as e:
            print(f"Error in TowerDataAPIView: {str(e)}")
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class ClearAlertsView(TemplateView):
    """View for clearing browser alerts"""
    template_name = 'monitoring/clear_alerts.html'
