from django.urls import path
from . import views

app_name = 'monitoring'

urlpatterns = [
    path('', views.LandingPageView.as_view(), name='landing'),
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
    path('alerts/', views.AlertListView.as_view(), name='alerts'),
    path('tower/<int:pk>/', views.TowerLightDetailView.as_view(), name='tower_detail'),
    path('api/tower-locations/', views.TowerLocationsView.as_view(), name='tower_locations'),
    path('api/tower-data/', views.TowerDataAPIView.as_view(), name='tower_data'),
    path('control/', views.ControlPanelView.as_view(), name='control'),
    path('manage-infrastructure/', views.ManageInfrastructureView.as_view(), name='manage_infrastructure'),
    path('alert/<int:pk>/', views.AlertDetailView.as_view(), name='alert_detail'),
    path('tower-lights/', views.TowerLightsView.as_view(), name='tower_lights'),
    path('clear-alerts/', views.ClearAlertsView.as_view(), name='clear_alerts'),
]

