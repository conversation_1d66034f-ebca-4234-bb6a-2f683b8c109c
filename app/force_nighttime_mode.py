"""
<PERSON><PERSON><PERSON> to force the system to think it's nighttime by temporarily modifying the code.
This will help test if the tower lights are using the correct thresholds when they're ON.
"""

import os
import sys
import re
import fileinput
import shutil
import time

def backup_file(file_path):
    """Create a backup of the file"""
    backup_path = file_path + '.bak'
    shutil.copy2(file_path, backup_path)
    print(f"Created backup: {backup_path}")
    return backup_path

def restore_file(backup_path, file_path):
    """Restore the file from backup"""
    shutil.copy2(backup_path, file_path)
    print(f"Restored file from backup: {file_path}")
    os.remove(backup_path)
    print(f"Removed backup: {backup_path}")

def modify_file(file_path, search_pattern, replacement):
    """Modify the file by replacing the search pattern with the replacement"""
    with fileinput.FileInput(file_path, inplace=True) as file:
        for line in file:
            print(re.sub(search_pattern, replacement, line), end='')
    print(f"Modified file: {file_path}")

def force_nighttime_mode():
    """Force the system to think it's nighttime"""
    try:
        # File paths
        api_views_path = 'app/api/views.py'
        monitoring_views_path = 'app/monitoring/views.py'
        
        # Create backups
        api_views_backup = backup_file(api_views_path)
        monitoring_views_backup = backup_file(monitoring_views_path)
        
        try:
            # Modify the is_nighttime check in api/views.py
            search_pattern = r'is_nighttime = \(current_hour >= 18 or current_hour < 6\)'
            replacement = 'is_nighttime = True  # Forced to True for testing'
            modify_file(api_views_path, search_pattern, replacement)
            
            # Modify the is_nighttime check in monitoring/views.py
            search_pattern = r'is_nighttime = \(current_hour >= 18 or current_hour < 6\)'
            replacement = 'is_nighttime = True  # Forced to True for testing'
            modify_file(monitoring_views_path, search_pattern, replacement)
            
            print("\nNighttime mode has been forced!")
            print("Please restart your Django server for the changes to take effect.")
            print("Then check the tower light values to see if they're using the correct thresholds.")
            
            # Wait for user to press Enter
            input("\nPress Enter when you're done testing to restore the original files...")
            
        finally:
            # Restore the original files
            restore_file(api_views_backup, api_views_path)
            restore_file(monitoring_views_backup, monitoring_views_path)
            
            print("\nOriginal files have been restored.")
            print("Please restart your Django server again for the changes to take effect.")
    
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    force_nighttime_mode()
