from rest_framework import viewsets, permissions, filters, status
from rest_framework.decorators import action
from rest_framework.response import Response
from django_filters.rest_framework import DjangoFilterBackend
from monitoring.models import TowerLight, FaultLog, Technician, MaintenanceSchedule, SensorData, TowerCommand
from .serializers import (
    TowerLightSerializer, FaultLogSerializer,
    TechnicianSerializer, MaintenanceScheduleSerializer, SensorDataSerializer
)
from django.utils import timezone
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from datetime import timedelta
import random

class TowerLightViewSet(viewsets.ModelViewSet):
    queryset = TowerLight.objects.all()
    serializer_class = TowerLightSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['status']
    search_fields = ['name']

    # Update tower light status
    @action(detail=True, methods=['post'])
    def update_status(self, request, pk=None):
        tower = self.get_object()
        status = request.data.get('status')
        if status in dict(TowerLight.STATUS_CHOICES):
            tower.status = status
            tower.save()
            return Response({'status': 'success'})
        return Response({'status': 'error', 'message': 'Invalid status'}, status=400)

    @action(detail=True, methods=['post'])
    def control(self, request, pk=None):
        tower = self.get_object()
        action = request.data.get('action')

        # Map web actions to microcontroller commands
        action_mapping = {
            'turn_on': 'manual_override_on',
            'turn_off': 'manual_override_off',
            'maintenance': 'maintenance_mode',
            'reset': 'reset',
        }

        # For towers 1 and 2 (microcontrollers), create a command
        if tower.id <= 2:
            command_type = action_mapping.get(action)
            if not command_type:
                return Response({
                    'status': 'error',
                    'message': f'Invalid action: {action}'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Create command with 5-minute expiry
            expires_at = timezone.now() + timedelta(minutes=5)

            # Set parameters based on action
            parameters = {}
            if action == 'turn_on':
                parameters = {'relay_state': True}
            elif action == 'turn_off':
                parameters = {'relay_state': False}

            command = TowerCommand.objects.create(
                tower=tower,
                command_type=command_type,
                parameters=parameters,
                expires_at=expires_at,
                created_by=request.user if request.user.is_authenticated else None
            )

            # Also update the database status for immediate UI feedback
            if action == 'turn_on':
                tower.status = 'active'
            elif action == 'turn_off':
                tower.status = 'inactive'
            elif action == 'maintenance':
                tower.status = 'maintenance'

            tower.save()

            return Response({
                'status': 'success',
                'message': f'Command {action} queued for {tower.name}',
                'command_id': command.id,
                'tower_status': tower.status
            })

        # For towers 3-20 (simulated), just update database status
        else:
            # Get current time
            current_time = timezone.now()
            harare_tz = timezone.get_current_timezone()
            current_hour = current_time.astimezone(harare_tz).hour
            current_minute = current_time.astimezone(harare_tz).minute

            # Check if it's nighttime
            is_nighttime = (current_hour == 17 and current_minute >= 59) or \
                          (current_hour >= 18 or current_hour < 6) or \
                          (current_hour == 5 and current_minute <= 59)

        if action == 'turn_on':
            # If turning on during day, update last_checked to mark as manually controlled
            if not is_nighttime:
                tower.last_checked = current_time
            tower.status = 'active'
        elif action == 'turn_off':
            tower.status = 'inactive'
        elif action == 'maintenance':
            tower.status = 'maintenance'
        else:
            return Response({
                'status': 'error',
                'message': 'Invalid action'
            }, status=status.HTTP_400_BAD_REQUEST)

        tower.save()
        return Response({
            'status': 'success',
            'message': f'Tower {tower.name} {action} successful',
            'is_manual_control': not is_nighttime and action == 'turn_on'
        })

class FaultLogViewSet(viewsets.ModelViewSet):
    queryset = FaultLog.objects.all()
    serializer_class = FaultLogSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['resolved', 'fault_type']
    search_fields = ['description']

    # Mark fault as resolved
    @action(detail=True, methods=['post'])
    def resolve(self, request, pk=None):
        fault = self.get_object()
        fault.resolved = True
        fault.resolved_by = request.user
        fault.resolved_at = timezone.now()
        fault.save()
        return Response({'status': 'success'})

class TechnicianViewSet(viewsets.ModelViewSet):
    queryset = Technician.objects.all()
    serializer_class = TechnicianSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['user__username', 'user__first_name', 'user__last_name', 'region']

class MaintenanceScheduleViewSet(viewsets.ModelViewSet):
    queryset = MaintenanceSchedule.objects.all()
    serializer_class = MaintenanceScheduleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['completed']
    search_fields = ['notes']

class DashboardDataView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def get(self, request):
        # Get counts for status cards
        status_counts = TowerLight.get_status_counts()
        status_dict = {item['status']: item['count'] for item in status_counts}

        # Get tower locations with status
        towers = TowerLight.objects.all()
        tower_data = [{
            'id': tower.id,
            'status': tower.status,
            'lat': tower.latitude,
            'lng': tower.longitude
        } for tower in towers]

        # Get recent alerts
        recent_alerts = FaultLog.get_recent_alerts()
        alert_data = [{
            'id': alert.id,
            'tower_id': alert.tower.id,
            'tower_name': alert.tower.name,
            'fault_type': alert.get_fault_type_display(),
            'timestamp': alert.timestamp.strftime("%B %d, %Y, %I:%M %p")
        } for alert in recent_alerts]

        # Get latest sensor data for each tower
        sensor_data = {}
        for tower in towers:
            latest_data = SensorData.get_latest_data(tower.id)
            if latest_data.exists():
                data = latest_data.first()
                sensor_data[tower.id] = {
                    'light_level': data.light_level,
                    'efficiency': data.efficiency,
                    'faulty_lights': data.faulty_lights,
                    'maintenance_lights': data.maintenance_lights,
                    'timestamp': data.timestamp.strftime("%Y-%m-%d %H:%M:%S")
                }

        # Calculate overall system efficiency from sensor data
        overall_efficiency = 0
        if sensor_data:
            efficiency_values = [data['efficiency'] for data in sensor_data.values()]
            overall_efficiency = sum(efficiency_values) / len(efficiency_values)

        return Response({
            'active_count': status_dict.get('active', 0),
            'faulty_count': status_dict.get('faulty', 0),
            'maintenance_count': status_dict.get('maintenance', 0),
            'towers': tower_data,
            'recent_alerts': alert_data,
            'sensor_data': sensor_data,
            'overall_efficiency': round(overall_efficiency, 1)
        })

@method_decorator(csrf_exempt, name='dispatch')
class TowerStatusUpdateView(APIView):
    permission_classes = []  # No authentication required for microcontroller updates

    # Update tower status and sensor data from microcontroller
    def post(self, request):
        tower_id = request.data.get('tower_id')
        status = request.data.get('status')
        fault_type = request.data.get('fault_type')

        # Only towers 1 and 2 are connected to microcontrollers and can send sensor data
        if not tower_id or int(tower_id) > 2:
            return Response({
                'error': 'Invalid tower ID. Only towers 1 and 2 can send sensor data.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            tower = TowerLight.objects.get(id=tower_id)

            # Update tower status if provided
            if status:
                tower.status = status
                tower.save()

                # Create fault log if tower is faulty
                if status == 'faulty' and fault_type:
                    # Generate appropriate description based on fault type
                    descriptions = {
                        'power': f"Power failure detected at {tower.name}. Voltage or current readings outside normal parameters.",
                        'bulb': f"Bulb failure detected at {tower.name}. Light output below expected levels.",
                        'connection': f"Connection issue detected at {tower.name}. Intermittent signal or communication problems.",
                        'other': f"Unknown fault detected at {tower.name}. System requires inspection."
                    }

                    # Use the mapped description or a default one
                    description = descriptions.get(fault_type, f"{fault_type.capitalize()} fault detected at {tower.name}")

                    FaultLog.objects.create(
                        tower=tower,
                        fault_type=fault_type,
                        description=description,
                        timestamp=timezone.now()
                    )

            # Create sensor data record if sensor data is provided
            if any(key in request.data for key in ['sensor_value', 'light_level', 'voltage', 'current', 'power']):
                # Extract sensor data
                sensor_data = {
                    'tower': tower.id,
                    'sensor_value': request.data.get('sensor_value', 0),
                    'light_level': request.data.get('light_level', 0),
                    'voltage': request.data.get('voltage'),
                    'current': request.data.get('current'),
                    'power': request.data.get('power'),
                    'faulty_lights': request.data.get('faulty_lights', 0),
                    'maintenance_lights': request.data.get('maintenance_lights', 0),
                    'efficiency': request.data.get('efficiency', 100)
                }

                # Create new sensor data record
                serializer = SensorDataSerializer(data=sensor_data)
                if serializer.is_valid():
                    serializer.save()
                    print(f"Saved sensor data for Tower {tower_id}: {sensor_data}")
                else:
                    print(f"Invalid sensor data: {serializer.errors}")
                    return Response({
                        'status': 'error',
                        'message': serializer.errors
                    }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'status': 'success',
                'message': f'Tower {tower_id} updated successfully'
            })
        except TowerLight.DoesNotExist:
            return Response(
                {'status': 'error', 'message': f'Tower {tower_id} not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'status': 'error', 'message': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@method_decorator(csrf_exempt, name='dispatch')
class SensorDataView(APIView):
    permission_classes = []  # No authentication required for microcontroller updates

    # Receive sensor data from microcontroller
    def post(self, request):
        # Get tower ID from the URL or default to 1
        tower_id = request.data.get('tower_id', 1)

        try:
            tower = TowerLight.objects.get(id=tower_id)

            # Log the incoming data for debugging
            print(f"Received sensor data: {request.data}")

            # Extract values with defaults
            sensor_value = request.data.get('sensor_value', 0)
            if isinstance(sensor_value, str) and sensor_value.isdigit():
                sensor_value = int(sensor_value)

            light_level = request.data.get('light_level', 0)
            if isinstance(light_level, str) and light_level.isdigit():
                light_level = int(light_level)

            # Get voltage, current, power with proper type conversion
            voltage = request.data.get('voltage')
            if voltage is not None:
                try:
                    voltage = float(voltage)
                except (ValueError, TypeError):
                    voltage = None

            current = request.data.get('current')
            if current is not None:
                try:
                    current = float(current)
                except (ValueError, TypeError):
                    current = None

            power = request.data.get('power')
            if power is not None:
                try:
                    power = float(power)
                except (ValueError, TypeError):
                    power = None

            # If voltage and current exist but power doesn't, calculate it
            if voltage is not None and current is not None and power is None:
                power = voltage * current

            # Create sensor data record
            serializer = SensorDataSerializer(data={
                'tower': tower.id,
                'sensor_value': sensor_value,
                'light_level': light_level,
                'faulty_lights': request.data.get('faulty_lights', 0),
                'maintenance_lights': request.data.get('maintenance_lights', 0),
                'efficiency': request.data.get('efficiency', 100),
                'voltage': voltage,
                'current': current,
                'power': power,
            })

            if serializer.is_valid():
                serializer.save()
                return Response({'status': 'success', 'data': serializer.data})
            else:
                return Response(
                    {'status': 'error', 'message': serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST
                )

        except TowerLight.DoesNotExist:
            return Response(
                {'status': 'error', 'message': 'Tower not found'},
                status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            return Response(
                {'status': 'error', 'message': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

class TowerDataView(APIView):
    permission_classes = []  # Allow unauthenticated access

    def get(self, request):
        # Get page parameter for pagination
        page = request.query_params.get('page', 1)
        try:
            page = int(page)
        except ValueError:
            page = 1

        # Number of towers per page
        towers_per_page = int(request.query_params.get('per_page', 9))

        # Get all towers - ensure we always have data to display
        towers = TowerLight.objects.all()

        # If no towers exist, create sample towers for demonstration
        if not towers.exists() or towers.count() < 12:
            # Create 12 sample towers for demonstration
            # First two will be active sensor devices, the rest will be regular towers
            sample_towers = [
                {'name': 'Tower 1 (Active Sensor)', 'latitude': -17.8252, 'longitude': 31.0335, 'status': 'active'},
                {'name': 'Tower 2 (Active Sensor)', 'latitude': -17.8350, 'longitude': 31.0450, 'status': 'active'},
            ]

            # Add 18 more regular towers around Harare, Zimbabwe
            for i in range(3, 21):
                # Generate positions around Harare
                lat_offset = (i % 5) * 0.01 - 0.02
                lng_offset = (i // 5) * 0.01 - 0.02

                # Set all towers 3-20 to active status
                # As per requirement, tower lights 3-20 should always be active
                status = 'active'

                sample_towers.append({
                    'name': f'Tower {i}',
                    'latitude': -17.8252 + lat_offset,
                    'longitude': 31.0335 + lng_offset,
                    'status': status
                })

            # Create or update towers
            for i, tower_data in enumerate(sample_towers):
                TowerLight.objects.update_or_create(
                    id=i+1,  # Ensure consistent IDs
                    defaults={
                        'name': tower_data['name'],
                        'latitude': tower_data['latitude'],
                        'longitude': tower_data['longitude'],
                        'status': tower_data['status']
                    }
                )

            # Refresh the queryset
            towers = TowerLight.objects.all()

        # Calculate pagination
        start_idx = (page - 1) * towers_per_page
        end_idx = start_idx + towers_per_page
        paginated_towers = towers[start_idx:end_idx]

        # Get current time in Harare timezone (GMT+2)
        harare_tz = timezone.pytz.timezone('Africa/Harare')
        current_time = timezone.now().astimezone(harare_tz)
        current_hour = current_time.hour
        current_minute = current_time.minute

        # Nighttime: 18:00 to 05:59
        is_nighttime = (current_hour >= 18 or current_hour < 6)
        print(f"[TowerDataView] Harare time: {current_time}, is_nighttime: {is_nighttime}")

        # Light intensity threshold for turning on lights (0-100)
        LIGHT_INTENSITY_THRESHOLD = 20

        # Prepare response data
        tower_data = []
        for tower in paginated_towers:
            latest_sensor_data = SensorData.get_latest_data(tower.id).first()
            tower_info = {
                'id': tower.id,
                'name': tower.name,
                'latitude': tower.latitude,
                'longitude': tower.longitude,
                'installation_date': tower.installation_date.strftime('%Y-%m-%d') if hasattr(tower, 'installation_date') and tower.installation_date else None,
                'sensor_data': None,
                'status': tower.status,
            }
            if tower.id == 1:
                # Tower 1: Only use real data from microcontroller
                # Keep the original status from the database
                if latest_sensor_data:
                    # Use the latest real sensor data
                    tower_info['status'] = tower.status
                    tower_info['sensor_data'] = {
                        'sensor_value': latest_sensor_data.sensor_value,
                        'light_level': latest_sensor_data.light_level,
                        'faulty_lights': latest_sensor_data.faulty_lights,
                        'maintenance_lights': latest_sensor_data.maintenance_lights,
                        'efficiency': latest_sensor_data.efficiency,
                        'voltage': latest_sensor_data.voltage,
                        'current': latest_sensor_data.current,
                        'power': latest_sensor_data.power,
                        'timestamp': latest_sensor_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    }
                else:
                    # No data received from microcontroller yet - show null values
                    tower_info['sensor_data'] = {
                        'sensor_value': None,
                        'light_level': None,
                        'faulty_lights': None,
                        'maintenance_lights': None,
                        'efficiency': None,
                        'voltage': None,
                        'current': None,
                        'power': None,
                        'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S')
                    }
            elif tower.id == 2:
                # Tower 2: Only use real data from microcontroller
                # Keep the original status from the database
                if latest_sensor_data:
                    # Use the latest real sensor data
                    tower_info['status'] = tower.status
                    tower_info['sensor_data'] = {
                        'sensor_value': latest_sensor_data.sensor_value,
                        'light_level': latest_sensor_data.light_level,
                        'faulty_lights': latest_sensor_data.faulty_lights,
                        'maintenance_lights': latest_sensor_data.maintenance_lights,
                        'efficiency': latest_sensor_data.efficiency,
                        'voltage': latest_sensor_data.voltage,
                        'current': latest_sensor_data.current,
                        'power': latest_sensor_data.power,
                        'timestamp': latest_sensor_data.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                    }
                else:
                    # No data received from microcontroller yet - show null values
                    tower_info['sensor_data'] = {
                        'sensor_value': None,
                        'light_level': None,
                        'faulty_lights': None,
                        'maintenance_lights': None,
                        'efficiency': None,
                        'voltage': None,
                        'current': None,
                        'power': None,
                        'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S')
                    }
            else:
                # All other towers (3-20): use the tower's actual status
                # 'active' status means the tower is fully functional (not faulty)
                # The light can be either ON or OFF based on time of day

                # Determine if the light should be ON or OFF based on time of day
                # Lights are ON at night (6pm-6am) and OFF during the day (6am-6pm)
                is_light_on = is_nighttime

                # Only turn on lights if it's night time AND the tower is not faulty/maintenance
                if is_light_on and tower.status != 'faulty' and tower.status != 'maintenance':
                    # When light is ON: voltage between 11.0V and 12.4V, current between 0.2A and 2A
                    voltage = round(random.uniform(11.0, 12.4), 1)
                    current = round(random.uniform(0.2, 2.0), 2)
                    power = round(voltage * current, 2)
                    efficiency = round(random.uniform(85, 95), 1)
                    light_level = round(random.uniform(75, 100), 0)  # Light intensity between 75-100%
                    is_on = True
                else:
                    # When light is OFF: voltage between 0V and 0.5V, current between 0A and 0.2A
                    # This applies to: daytime, faulty towers, and maintenance towers
                    voltage = round(random.uniform(0.0, 0.5), 1)
                    current = round(random.uniform(0.0, 0.2), 2)
                    power = round(voltage * current, 2)
                    efficiency = 0
                    light_level = round(random.uniform(10, 20), 0)
                    is_on = False
                # Set faulty_lights and maintenance_lights based on tower status
                faulty_lights = 1 if tower.status == 'faulty' else 0
                maintenance_lights = 1 if tower.status == 'maintenance' else 0

                tower_info['sensor_data'] = {
                    'sensor_value': 3000,
                    'light_level': light_level,
                    'faulty_lights': faulty_lights,
                    'maintenance_lights': maintenance_lights,
                    'efficiency': efficiency,
                    'voltage': voltage,
                    'current': current,
                    'power': power,
                    'timestamp': current_time.strftime('%Y-%m-%d %H:%M:%S')
                }
            tower_data.append(tower_info)

        # Calculate total pages
        total_towers = towers.count()
        total_pages = (total_towers + towers_per_page - 1) // towers_per_page

        return Response({
            'towers': tower_data,
            'pagination': {
                'current_page': page,
                'total_pages': total_pages,
                'total_towers': total_towers,
                'towers_per_page': towers_per_page
            },
            'system_status': {
                'is_nighttime': is_nighttime,
                'current_time': current_time.strftime('%Y-%m-%d %H:%M:%S'),
                'light_intensity_threshold': LIGHT_INTENSITY_THRESHOLD,
                'auto_control': True
            }
        })

    def post(self, request):
        """Handle sensor data updates from microcontrollers"""
        tower_id = request.data.get('tower_id')
        # Only towers 1 and 2 are connected to microcontrollers and can send sensor data
        if not tower_id or int(tower_id) > 2:
            return Response({
                'error': 'Invalid tower ID. Only towers 1 and 2 can send sensor data.'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            tower = TowerLight.objects.get(id=tower_id)

            # Extract sensor data
            sensor_data = {
                'tower': tower.id,
                'sensor_value': request.data.get('sensor_value', 0),
                'light_level': request.data.get('light_level', 0),
                'voltage': request.data.get('voltage'),
                'current': request.data.get('current'),
                'power': request.data.get('power'),
                'faulty_lights': request.data.get('faulty_lights', 0),
                'maintenance_lights': request.data.get('maintenance_lights', 0),
                'efficiency': request.data.get('efficiency', 100)
            }

            # Create new sensor data record
            serializer = SensorDataSerializer(data=sensor_data)
            if serializer.is_valid():
                serializer.save()
                return Response({
                    'status': 'success',
                    'message': f'Sensor data updated for Tower {tower_id}'
                })
            else:
                return Response({
                    'status': 'error',
                    'message': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        except TowerLight.DoesNotExist:
            return Response({
                'status': 'error',
                'message': f'Tower {tower_id} not found'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            return Response({
                'status': 'error',
                'message': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

class ActiveAlertsView(APIView):
    permission_classes = []  # Allow unauthenticated access for the modal system

    def get(self, request):
        # Get all unresolved faults
        active_faults = FaultLog.objects.filter(resolved=False).order_by('-timestamp')

        # Serialize the data
        alerts = []
        for fault in active_faults:
            alerts.append({
                'id': fault.id,
                'tower_id': fault.tower.id,
                'tower_name': fault.tower.name,
                'fault_type': fault.fault_type,
                'description': fault.description,
                'timestamp': fault.timestamp.strftime("%Y-%m-%d %H:%M:%S"),
                'level': 'critical' if fault.fault_type in ['power', 'bulb', 'connection'] else 'warning'
            })

        return Response({
            'alerts': alerts,
            'count': len(alerts)
        })

@method_decorator(csrf_exempt, name='dispatch')
class TowerCommandView(APIView):
    permission_classes = []  # No authentication required for microcontroller polling

    def get(self, request, tower_id):
        """Get pending commands for a specific tower (microcontroller polling)"""
        try:
            # Only towers 1 and 2 can poll for commands
            if int(tower_id) > 2:
                return Response({
                    'error': 'Invalid tower ID. Only towers 1 and 2 can poll for commands.'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Clean up expired commands first
            TowerCommand.cleanup_expired_commands()

            # Get pending commands for this tower
            pending_commands = TowerCommand.get_pending_commands(tower_id)

            commands_data = []
            for command in pending_commands:
                commands_data.append({
                    'id': command.id,
                    'command_type': command.command_type,
                    'parameters': command.parameters,
                    'created_at': command.created_at.isoformat(),
                    'expires_at': command.expires_at.isoformat()
                })
                # Mark as sent when microcontroller polls for it
                command.mark_sent()

            return Response({
                'tower_id': tower_id,
                'commands': commands_data,
                'count': len(commands_data)
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def post(self, request, tower_id):
        """Handle command acknowledgment and completion from microcontroller"""
        try:
            # Only towers 1 and 2 can send command responses
            if int(tower_id) > 2:
                return Response({
                    'error': 'Invalid tower ID. Only towers 1 and 2 can send command responses.'
                }, status=status.HTTP_400_BAD_REQUEST)

            command_id = request.data.get('command_id')
            response_type = request.data.get('response_type')  # 'acknowledged' or 'completed'
            response_data = request.data.get('response_data', {})
            error_message = request.data.get('error_message', '')

            if not command_id:
                return Response({
                    'error': 'command_id is required'
                }, status=status.HTTP_400_BAD_REQUEST)

            try:
                command = TowerCommand.objects.get(id=command_id, tower_id=tower_id)
            except TowerCommand.DoesNotExist:
                return Response({
                    'error': 'Command not found'
                }, status=status.HTTP_404_NOT_FOUND)

            if response_type == 'acknowledged':
                command.mark_acknowledged()
            elif response_type == 'completed':
                command.mark_completed(response_data)
            elif response_type == 'failed':
                command.mark_failed(error_message)
            else:
                return Response({
                    'error': 'Invalid response_type. Must be acknowledged, completed, or failed.'
                }, status=status.HTTP_400_BAD_REQUEST)

            return Response({
                'status': 'success',
                'message': f'Command {command_id} marked as {response_type}'
            })

        except Exception as e:
            return Response({
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)