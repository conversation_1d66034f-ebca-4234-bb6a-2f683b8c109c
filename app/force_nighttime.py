"""
Script to test tower light values by making a direct API call.
This will check if the tower lights are using the correct thresholds.
"""

import os
import sys
import django
import requests
import json
import time

# Set up Django environment
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'app.settings')
django.setup()

def test_tower_values():
    """Test tower light values by making a direct API call"""
    try:
        print("\nTesting Tower Light Values")
        print("=========================")

        # Make a direct API call to get tower data
        print("Making API request to get tower data...")

        try:
            response = requests.get('http://localhost:8000/api/tower-data/')
            if response.status_code != 200:
                print(f"❌ API request failed with status code {response.status_code}")
                print("Please make sure your Django server is running on http://localhost:8000")
                return

            data = response.json()

            # Check if system_status is available
            if 'system_status' in data:
                is_nighttime = data['system_status']['is_nighttime']
                current_time = data['system_status']['current_time']
                print(f"Current system time: {current_time}")
                print(f"Is nighttime: {is_nighttime}")
            else:
                # Try to determine if it's nighttime based on the current hour
                from datetime import datetime
                current_time = datetime.now()
                current_hour = current_time.hour
                is_nighttime = (current_hour >= 18 or current_hour < 6)
                print(f"Current time: {current_time}")
                print(f"Is nighttime (based on local time): {is_nighttime}")

            # Check tower values
            print("\nTower Values:")
            print("-------------")
            for tower in data['towers']:
                if 3 <= tower['id'] <= 20:
                    status = tower['status']
                    voltage = tower['sensor_data']['voltage']
                    current = tower['sensor_data']['current']
                    power = tower['sensor_data']['power']
                    light_level = tower['sensor_data']['light_level']

                    # Determine expected ranges based on time of day
                    if is_nighttime and status == 'active':
                        # When light is ON
                        expected_voltage_range = "11.0V-12.4V"
                        expected_current_range = "0.2A-2.0A"
                        expected_light_range = "75-100%"
                        is_valid_voltage = 11.0 <= voltage <= 12.4
                        is_valid_current = 0.2 <= current <= 2.0
                        is_valid_light = 75 <= light_level <= 100
                        expected_state = "ON"
                    else:
                        # When light is OFF
                        expected_voltage_range = "0V-0.5V"
                        expected_current_range = "0A-0.2A"
                        expected_light_range = "10-20%"
                        is_valid_voltage = 0.0 <= voltage <= 0.5
                        is_valid_current = 0.0 <= current <= 0.2
                        is_valid_light = 10 <= light_level <= 20
                        expected_state = "OFF"

                    # Check if power is calculated correctly
                    calculated_power = round(voltage * current, 2)
                    is_power_correct = abs(power - calculated_power) < 0.1  # Allow for small rounding differences

                    print(f"Tower {tower['id']}: Status={status}, Expected state={expected_state}")
                    print(f"  Voltage={voltage}V (Expected: {expected_voltage_range})")
                    print(f"  Current={current}A (Expected: {expected_current_range})")
                    print(f"  Light={light_level}% (Expected: {expected_light_range})")
                    print(f"  Power={power}W (Calculated: {calculated_power}W)")

                    if not is_valid_voltage:
                        print(f"  ❌ Voltage is outside expected range!")
                    else:
                        print(f"  ✅ Voltage is within expected range")

                    if not is_valid_current:
                        print(f"  ❌ Current is outside expected range!")
                    else:
                        print(f"  ✅ Current is within expected range")

                    if not is_valid_light:
                        print(f"  ❌ Light intensity is outside expected range!")
                    else:
                        print(f"  ✅ Light intensity is within expected range")

                    if not is_power_correct:
                        print(f"  ❌ Power is not calculated correctly!")
                    else:
                        print(f"  ✅ Power is calculated correctly")

                    print("")  # Add a blank line between towers

            # Provide a summary
            if is_nighttime:
                print("\nSince it's nighttime, active tower lights should be ON with:")
                print("- Voltage: 11.0V-12.4V")
                print("- Current: 0.2A-2.0A")
                print("- Light intensity: 75-100%")
            else:
                print("\nSince it's daytime, tower lights should be OFF with:")
                print("- Voltage: 0V-0.5V")
                print("- Current: 0A-0.2A")
                print("- Light intensity: 10-20%")

            print("\nIf the values don't match the expected ranges, there might be an issue with the code.")

        except requests.exceptions.ConnectionError:
            print("❌ Could not connect to the Django server")
            print("Please make sure your Django server is running on http://localhost:8000")

    except Exception as e:
        print(f"Error during test: {e}")

if __name__ == "__main__":
    test_tower_values()
