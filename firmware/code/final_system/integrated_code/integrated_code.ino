#include <WiFi.h>
#include <HTTPClient.h>
#include <ArduinoJson.h> // Install from Library Manager
#include <math.h>       // For max() in getLightLux

// --- WiFi Credentials ---
const char* ssid = "YOUR_WIFI_SSID";
const char* password = "YOUR_WIFI_PASSWORD";

// --- Django Endpoints & Config ---
// *** UPDATE THESE WITH YOUR SERVER DETAILS ***
const char* djangoHost = "YOUR_DJANGO_IP_OR_DOMAIN"; // e.g., "*************:8000" or "yourdomain.com"
const int djangoPort = 81; // Or 8000, 443 (for HTTPS) etc.
String sensorDataUrl = String("http://") + djangoHost + "/api/monitoring/sensor-data/";
String statusUpdateUrl = String("http://") + djangoHost + "/api/tower-status-update/";

// --- Device Configuration ---
const int TOWER_ID_INT = 1; // Use the integer ID for your tower

// --- Pin Definitions (Consolidated & Updated) ---
// Sensors
#define VOLTAGE_PIN 32      // ADC1_CH6
#define CURRENT_PIN 34      // ADC1_CH7
#define LDR_PIN     33      // ADC1_CH5 (From user's LDR code)
// Simulation Inputs
#define POT_SIM_PIN 36      // ADC1_CH0 (Moved from 33)
#define BTN_LOW_LIGHT_PIN 25
#define BTN_WARN_PIN 26      // Mapped to maintenance flag
#define BTN_DECLINE_PIN 27   // Mapped to connection fault simulation
// Output
#define RELAY_PIN   21      // GPIO32 (From user's LDR code for relay) - MAKE SURE THIS IS OKAY FOR YOUR BOARD

// --- Sensor Calibration & Constants ---
#define VOLTAGE_DIVIDER_RATIO 5.0
#define ACS712_SENSITIVITY 0.100 // V/A (100mV/A for 20A version)
#define ACS712_ZERO_VOLTAGE 1.65 // Approx VCC/2 for 3.3V supply - CALIBRATE THIS
#define ADC_MAX 4095.0
#define ADC_VREF 3.3
#define NUM_CURRENT_SAMPLES 1 // Read single sample, filter handles smoothing

// --- LDR & Lux Calibration (From user's LDR code) ---
#define LDR_RAW_ADC_MIN 18     // Calibration: Raw value in brightest condition (Example: 18) - CALIBRATE
#define LDR_RAW_ADC_MAX 4095   // Calibration: Raw value in darkest condition (Example: 4095) - CALIBRATE
const double LUX_EST_M = 18061.92; // Derived slope for 1/Value mapping - RECALIBRATE IF NEEDED
const double LUX_EST_C = -3.41;    // Derived intercept for 1/Value mapping - RECALIBRATE IF NEEDED
#define MAX_EXPECTED_LUX 1000.0 // Used for mapping Lux back to 0-100% for API 'light_level'

// --- Relay Switching Thresholds (From user's LDR code) ---
const double RELAY_ON_LUX_THRESHOLD = 20.0;  // Turn relay ON when light falls BELOW this Lux level
const double RELAY_OFF_LUX_THRESHOLD = 50.0; // Turn relay OFF when light rises ABOVE this Lux level (Hysteresis)

// --- Fault Thresholds ---
#define VOLTAGE_LOW_THRESHOLD 10.0 // Volts
#define VOLTAGE_HIGH_THRESHOLD 14.0 // Volts
#define CURRENT_LOW_THRESHOLD 0.1  // Amps (Check only when light should be ON)
#define CURRENT_HIGH_THRESHOLD 5.0  // Amps (Adjust based on expected load)
#define POWER_LOW_THRESHOLD 1.0    // Watts (Check only when light should be ON)
// Light dark threshold % - use calculated percentage now
#define LIGHT_PERCENT_DARK_THRESHOLD 10 // Less than 10% light considered "dark"

// --- Butterworth Filter Configuration (From user's LDR code) ---
// Assuming 5 Hz sampling rate (READ_INTERVAL_MS = 200ms) and 0.2 Hz cutoff
// If READ_INTERVAL_MS changes, RECALCULATE these coefficients!
const unsigned long READ_INTERVAL_MS = 200; // How often to read sensors (milliseconds)
const double BUTTER_B0 = 0.013368;
const double BUTTER_B1 = 0.026736;
const double BUTTER_B2 = 0.013368;
const double BUTTER_A1 = -1.647198;
const double BUTTER_A2 = 0.700802;

// Filter state variables for EACH analog sensor
// LDR
double ldr_x_hist[2] = {0.0, 0.0};
double ldr_y_hist[2] = {0.0, 0.0};
bool ldr_filterInitialized = false;
// Voltage
double volt_x_hist[2] = {0.0, 0.0};
double volt_y_hist[2] = {0.0, 0.0};
bool volt_filterInitialized = false;
// Current (Sensor Voltage)
double curr_v_x_hist[2] = {0.0, 0.0};
double curr_v_y_hist[2] = {0.0, 0.0};
bool curr_v_filterInitialized = false;
// Potentiometer
double pot_x_hist[2] = {0.0, 0.0};
double pot_y_hist[2] = {0.0, 0.0};
bool pot_filterInitialized = false;


// --- Global Variables ---
// Filtered Sensor Values
double filteredVoltage = 0.0;
double filteredCurrent = 0.0;
double filteredPower = 0.0;
double filteredLdrAdc = 0.0;
double estimatedLux = 0.0;
int currentLightLevelPercent = 0; // Calculated percentage from filtered ADC
double filteredPotSimAdc = 0.0;

// Fault Flags (use filtered values for detection)
bool fault_undervoltage = false;
bool fault_overvoltage = false;
bool fault_low_current = false;
bool fault_high_current = false;
bool fault_low_power = false;
bool fault_sim_low_light_btn = false;
bool fault_sim_maintenance_btn = false;
bool fault_sim_connection_btn = false;
bool fault_sim_pot_triggered = false;

// State Variables
bool relayIsOn = false; // Tracks the actual relay state controlled by Lux
String currentTowerStatus = "inactive";
String previousTowerStatus = "inactive";
String currentFaultType = "";

// Timing (using millis() for non-blocking delays)
unsigned long lastReadTime = 0;
unsigned long lastDataSendTime = 0;
unsigned long lastStatusUpdateTime = 0;
const long dataSendInterval = 5000;      // Send sensor data every 5 seconds
const long statusUpdateInterval = 10000; // Send status heartbeat every 10 seconds (or sooner if status changes)

// --- Function Prototypes ---
void connectWiFi();
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized); // Updated signature
double readFilteredVoltage();
double readFilteredCurrent();
double readFilteredLdrAdc();
double readFilteredPotAdc();
double calculatePower(double voltage, double current);
double getLightLux(double filteredAdcValue); // From user code
int calculateLightLevelPercent(double filteredAdcValue); // Updated signature
String getQualitativeLightLevel(double luxValue); // From user code
void checkSimulationInputs();
void detectFaults();
String determineTowerStatus();
String determineFaultType();
void controlRelayBasedOnLux(); // New function for relay logic
void sendSensorData();
void sendStatusUpdate();
// Removed controlLight(bool), use controlRelayBasedOnLux now

// ========================== SETUP ==========================
void setup() {
  Serial.begin(115200);
  Serial.println("\n--- Tower Light Monitoring System Booting (Integrated LDR Filter/Relay Code) ---");

  // Configure Pins
  pinMode(VOLTAGE_PIN, INPUT);
  pinMode(CURRENT_PIN, INPUT);
  pinMode(LDR_PIN, INPUT);
  pinMode(POT_SIM_PIN, INPUT);
  pinMode(BTN_LOW_LIGHT_PIN, INPUT_PULLUP);
  pinMode(BTN_WARN_PIN, INPUT_PULLUP);      // Maintenance Sim
  pinMode(BTN_DECLINE_PIN, INPUT_PULLUP);   // Connection Fault Sim
  pinMode(RELAY_PIN, OUTPUT);              // Relay control pin

  Serial.print("LDR Pin: "); Serial.println(LDR_PIN);
  Serial.print("Relay Pin: "); Serial.println(RELAY_PIN);
  Serial.print("Voltage Pin: "); Serial.println(VOLTAGE_PIN);
  Serial.print("Current Pin: "); Serial.println(CURRENT_PIN);
  Serial.print("Pot Sim Pin: "); Serial.println(POT_SIM_PIN);
  Serial.print("Read Interval (ms): "); Serial.println(READ_INTERVAL_MS);
  Serial.print("Filter Cutoff Freq (Hz): ~"); Serial.println(0.2); // Matches user LDR code
  Serial.println("Relay control assumes HIGH activates the relay. Adjust digitalWrite if your relay is active LOW.");


  connectWiFi();

  // --- Initial Sensor Read & Relay State ---
  // Read all sensors once to initialize filters and state
  filteredLdrAdc = readFilteredLdrAdc(); // This initializes the LDR filter
  estimatedLux = getLightLux(filteredLdrAdc);

  // Initialize other filters (read once)
  readFilteredVoltage();
  readFilteredCurrent();
  readFilteredPotAdc();

  Serial.print("Initial Estimated Lux: "); Serial.println(estimatedLux, 1);

  // Set initial relay state based on Lux
  if (estimatedLux < RELAY_ON_LUX_THRESHOLD) {
      digitalWrite(RELAY_PIN, HIGH); // Assuming HIGH turns the relay ON
      relayIsOn = true;
      Serial.println("Initial light is low: Relay is ON.");
  } else {
      digitalWrite(RELAY_PIN, LOW); // Assuming LOW turns the relay OFF
      relayIsOn = false;
      Serial.println("Initial light is high: Relay is OFF.");
  }

  // Initialize status
  currentTowerStatus = determineTowerStatus(); // Use the determined relay state
  previousTowerStatus = currentTowerStatus;
  lastReadTime = millis(); // Initialize read timer

  Serial.println("-----------------------------------------------------");
  Serial.println("Setup Complete. Starting main loop.");
}

// ========================== LOOP ==========================
void loop() {
  unsigned long currentTime = millis();

  // --- Check WiFi Connection ---
  if (WiFi.status() != WL_CONNECTED) {
    Serial.println("WiFi Disconnected. Attempting to reconnect...");
    connectWiFi();
    if (WiFi.status() != WL_CONNECTED) {
        delay(1000); return; // Wait before retrying loop
    }
  }

  // --- Read Sensors Periodically ---
  if (currentTime - lastReadTime >= READ_INTERVAL_MS) {
    lastReadTime = currentTime; // Reset read timer

    // 1. Read Filtered Sensor Values
    filteredVoltage = readFilteredVoltage();
    filteredCurrent = readFilteredCurrent();
    filteredLdrAdc = readFilteredLdrAdc();
    filteredPotSimAdc = readFilteredPotAdc(); // Read simulation pot

    // 2. Calculate Derived Values
    filteredPower = calculatePower(filteredVoltage, filteredCurrent);
    estimatedLux = getLightLux(filteredLdrAdc);
    currentLightLevelPercent = calculateLightLevelPercent(filteredLdrAdc);

    // 3. Check Simulation Inputs (Buttons read instantly, Pot needs periodic read)
    checkSimulationInputs(); // Reads buttons and checks filtered pot value

    // 4. Detect Faults (using filtered values)
    detectFaults();

    // 5. Determine Current Status (based on faults and relay state)
    currentTowerStatus = determineTowerStatus();
    if (currentTowerStatus == "faulty") {
        currentFaultType = determineFaultType();
    } else {
        currentFaultType = ""; // Clear fault type if not faulty
    }

    // 6. Control Relay Based on Estimated Lux (Local Automatic Control)
    controlRelayBasedOnLux(); // Updates relayIsOn

    // DEBUG Output (Optional)
    // Serial.print("V: "); Serial.print(filteredVoltage, 1);
    // Serial.print(" A: "); Serial.print(filteredCurrent, 2);
    // Serial.print(" W: "); Serial.print(filteredPower, 1);
    // Serial.print(" LDR(ADC): "); Serial.print((int)filteredLdrAdc);
    // Serial.print(" Lux: "); Serial.print(estimatedLux, 1);
    // Serial.print(" Light%: "); Serial.print(currentLightLevelPercent);
    // Serial.print(" Relay: "); Serial.print(relayIsOn ? "ON" : "OFF");
    // Serial.print(" Status: "); Serial.println(currentTowerStatus);

  } // End periodic sensor read block

  // --- Network Communication (Independent Timing) ---

  // 7. Send Sensor Data Periodically
  if (currentTime - lastDataSendTime >= dataSendInterval) {
    // Ensure we have recent filtered data before sending
    if(filteredLdrAdc != 0.0) { // Check if sensors have been read at least once
       sendSensorData();
       lastDataSendTime = currentTime;
    }
  }

  // 8. Send Status Update Periodically or On Change
  bool statusChanged = (currentTowerStatus != previousTowerStatus);
  if (statusChanged || (currentTime - lastStatusUpdateTime >= statusUpdateInterval)) {
    // Ensure we have recent status data before sending
     if(currentTowerStatus != "") { // Check if status determined at least once
        sendStatusUpdate();
        lastStatusUpdateTime = currentTime;
        previousTowerStatus = currentTowerStatus; // Update previous status after sending
     }
  }

  // No main loop delay needed due to interval timing
}

// ========================== WiFi Connection ==========================
void connectWiFi() {
  if (WiFi.status() == WL_CONNECTED) return;
  Serial.print("Connecting to WiFi: "); Serial.println(ssid);
  WiFi.mode(WIFI_STA); WiFi.begin(ssid, password);
  int retries = 0;
  while (WiFi.status() != WL_CONNECTED && retries < 20) { delay(500); Serial.print("."); retries++; }
  if(WiFi.status() != WL_CONNECTED) { Serial.println("\nFailed to connect to WiFi."); }
  else { Serial.println("\nWiFi Connected!"); Serial.print("IP: "); Serial.println(WiFi.localIP()); }
}

// ========================== Filter Function ==========================
/**
 * @brief Applies a 2nd order Butterworth low-pass filter using provided state.
 * @param rawValue The current raw input value (x[n]).
 * @param x_hist Array storing previous 2 input values x[n-1], x[n-2].
 * @param y_hist Array storing previous 2 output values y[n-1], y[n-2].
 * @param initialized Flag indicating if the filter history has been initialized.
 * @return The filtered output value (y[n]).
 */
double applyButterworth(double rawValue, double x_hist[], double y_hist[], bool &initialized) {
  double xn = rawValue; // Current input x[n]

  if (!initialized) {
      x_hist[0] = xn; x_hist[1] = xn;
      y_hist[0] = xn; y_hist[1] = xn;
      initialized = true;
  }
  // Difference equation: y[n] = B0*x[n] + B1*x[n-1] + B2*x[n-2] - A1*y[n-1] - A2*y[n-2]
  double yn = BUTTER_B0 * xn + BUTTER_B1 * x_hist[0] + BUTTER_B2 * x_hist[1] - BUTTER_A1 * y_hist[0] - BUTTER_A2 * y_hist[1];
  // Update history
  x_hist[1] = x_hist[0]; x_hist[0] = xn;
  y_hist[1] = y_hist[0]; y_hist[0] = yn;
  return yn;
}

// ============ Filtered Sensor Reading Functions ============

double readFilteredVoltage() {
  int rawAdc = analogRead(VOLTAGE_PIN);
  double sensorOutputVoltage = (rawAdc / ADC_MAX) * ADC_VREF;
  double actualVoltage = sensorOutputVoltage * VOLTAGE_DIVIDER_RATIO;
  // Apply filter to the calculated actual voltage
  double filteredV = applyButterworth(actualVoltage > 0.1 ? actualVoltage : 0.0, volt_x_hist, volt_y_hist, volt_filterInitialized);
  return filteredV;
}

double readFilteredCurrent() {
  int rawAdc = analogRead(CURRENT_PIN);
  double sensorVoltage = (rawAdc / ADC_MAX) * ADC_VREF;
  // Apply filter to the sensor voltage BEFORE calculating current
  double filteredSensorVoltage = applyButterworth(sensorVoltage, curr_v_x_hist, curr_v_y_hist, curr_v_filterInitialized);
  double current = (filteredSensorVoltage - ACS712_ZERO_VOLTAGE) / ACS712_SENSITIVITY;
  return abs(current) > 0.05 ? current : 0.0; // Noise filter near zero
}

double readFilteredLdrAdc() {
    int rawAdc = analogRead(LDR_PIN);
    // Apply filter directly to the raw ADC reading for LDR
    double filteredAdc = applyButterworth((double)rawAdc, ldr_x_hist, ldr_y_hist, ldr_filterInitialized);
    return filteredAdc;
}

double readFilteredPotAdc() {
    int rawAdc = analogRead(POT_SIM_PIN);
    // Apply filter directly to the raw ADC reading for the Potentiometer
    double filteredAdc = applyButterworth((double)rawAdc, pot_x_hist, pot_y_hist, pot_filterInitialized);
    return filteredAdc;
}


// ============ Calculation & Helper Functions ============

double calculatePower(double voltage, double current) {
  return abs(voltage * current);
}

// --- Functions from user's LDR code ---
double getLightLux(double filteredAdcValue) {
  // Ensure input value is within a sensible range, clamp lower bound at 1.0
  double value = constrain(filteredAdcValue, 1.0, 4095.0);
  // Apply the linear formula on the inverse of the value
  double estimatedLux = LUX_EST_M / value + LUX_EST_C;
  // Ensure Lux is not negative, allow high values, optional lower clamp
  estimatedLux = max(0.5, estimatedLux); // Lux cannot be negative, min display 0.5 Lux
  return estimatedLux;
}

int calculateLightLevelPercent(double filteredAdcValue) {
    // Map filtered ADC value (lower = brighter) inversely to 0-100% (higher = brighter)
    // Use calibrated min/max RAW ADC values for mapping range
    double constrainedAdc = constrain(filteredAdcValue, LDR_RAW_ADC_MIN, LDR_RAW_ADC_MAX);
    // Map the constrained value inversely to 0-100%
    double percent = 100.0 - ( ( (constrainedAdc - LDR_RAW_ADC_MIN ) / (LDR_RAW_ADC_MAX - LDR_RAW_ADC_MIN) ) * 100.0 );
    return constrain((int)round(percent), 0, 100); // Ensure result is 0-100 integer
}

String getQualitativeLightLevel(double luxValue) {
  if (luxValue > 1000.0) return "Very Bright";
  else if (luxValue > 200.0) return "Bright";
  else if (luxValue > 50.0) return "Moderate";
  else if (luxValue > 10.0) return "Dim";
  else return "Dark";
}
// --- End functions from user's LDR code ---

// ====================== Simulation Input Handling =====================
void checkSimulationInputs() {
    fault_sim_low_light_btn = (digitalRead(BTN_LOW_LIGHT_PIN) == LOW);
    fault_sim_maintenance_btn = (digitalRead(BTN_WARN_PIN) == LOW);
    fault_sim_connection_btn = (digitalRead(BTN_DECLINE_PIN) == LOW);

    // Check filtered Potentiometer ADC value for simulation trigger
    if (filteredPotSimAdc < 500 || filteredPotSimAdc > 3500) { // Use filtered value
        fault_sim_pot_triggered = true;
    } else {
        fault_sim_pot_triggered = false;
    }
}

// ========================== Fault Detection Logic ==========================
void detectFaults() {
  // Reset real faults each cycle
  fault_undervoltage = false; fault_overvoltage = false;
  fault_low_current = false; fault_high_current = false; fault_low_power = false;

  // Use FILTERED values for detection
  if (filteredVoltage < VOLTAGE_LOW_THRESHOLD && filteredVoltage > 0.5) fault_undervoltage = true;
  else if (filteredVoltage > VOLTAGE_HIGH_THRESHOLD) fault_overvoltage = true;

  if (filteredCurrent > CURRENT_HIGH_THRESHOLD) fault_high_current = true;

  // Check low current/power only if relay is ON and voltage OK
  if (relayIsOn && !fault_undervoltage && !fault_overvoltage) {
      if (filteredCurrent < CURRENT_LOW_THRESHOLD) fault_low_current = true; // Potential bulb failure
      if (filteredPower < POWER_LOW_THRESHOLD) fault_low_power = true;
  }
  // Note: Actual darkness is handled by relay control, not flagged as a fault here.
}

// =================== Status Determination ==================
String determineTowerStatus() {
    // Use relayIsOn for active/inactive state
    bool hasMajorFault = fault_undervoltage || fault_overvoltage || fault_high_current || fault_low_current;
    if (hasMajorFault || fault_sim_connection_btn) return "faulty";
    else if (fault_sim_maintenance_btn) return "maintenance";
    else if (relayIsOn) return "active"; // Based on relay state now
    else return "inactive";
}

String determineFaultType() {
    if (fault_undervoltage || fault_overvoltage || fault_high_current) return "power";
    else if (fault_low_current) return "bulb"; // Bulb failure likely if low current when ON
    else if (fault_sim_connection_btn) return "connection";
    else return "unknown";
}

// =================== Relay Control Logic ===================
void controlRelayBasedOnLux() {
  // Uses Hysteresis based on estimated Lux
  bool previousRelayState = relayIsOn;

  if (!relayIsOn && estimatedLux < RELAY_ON_LUX_THRESHOLD) {
      // Turn ON
      digitalWrite(RELAY_PIN, HIGH); // Assuming HIGH turns the relay ON
      relayIsOn = true;
      Serial.print(millis()); Serial.print(" Lux < "); Serial.print(RELAY_ON_LUX_THRESHOLD, 1); Serial.println(" -> Relay ON");
  } else if (relayIsOn && estimatedLux > RELAY_OFF_LUX_THRESHOLD) {
      // Turn OFF
      digitalWrite(RELAY_PIN, LOW); // Assuming LOW turns the relay OFF
      relayIsOn = false;
      Serial.print(millis()); Serial.print(" Lux > "); Serial.print(RELAY_OFF_LUX_THRESHOLD, 1); Serial.println(" -> Relay OFF");
  }
  // If state changed, update status immediately
  // if (relayIsOn != previousRelayState) {
  //    currentTowerStatus = determineTowerStatus();
  // }
}


// ========================== Network Communication ==========================
void sendSensorData() {
  if (WiFi.status() != WL_CONNECTED) { Serial.println("sendSensorData: WiFi Disconnected."); return; }

  HTTPClient http; http.begin(sensorDataUrl); http.addHeader("Content-Type", "application/json");
  // *** FIX: Specify the size for StaticJsonDocument ***
  StaticJsonDocument<512> doc; // Allocate 512 bytes (adjust if needed)

  // Required fields
  doc["tower_id"] = TOWER_ID_INT;
  doc["sensor_value"] = (int)round(filteredLdrAdc); // Send filtered LDR ADC as sensor_value
  doc["light_level"] = currentLightLevelPercent; // Send calculated percentage

  // Optional fields (using filtered values)
  bool isFaulty = (currentTowerStatus == "faulty");
  doc["faulty_lights"] = isFaulty ? 1 : 0;
  doc["maintenance_lights"] = fault_sim_maintenance_btn ? 1 : 0;
  doc["efficiency"] = fault_high_current ? 80 : 100;
  doc["voltage"] = filteredVoltage;
  doc["current"] = filteredCurrent;
  doc["power"] = filteredPower;
  // Add estimated Lux if API can handle it (optional)
  // doc["estimated_lux"] = estimatedLux;

  String jsonPayload; serializeJson(doc, jsonPayload);

  Serial.print(millis()); Serial.print(" Sending Sensor Data: "); Serial.println(jsonPayload);
  int httpResponseCode = http.POST(jsonPayload);

  if (httpResponseCode > 0) {
    Serial.print("Sensor Data Response Code: "); Serial.println(httpResponseCode);
    if (httpResponseCode != HTTP_CODE_OK) { String responsePayload = http.getString(); Serial.println("Response: " + responsePayload); }
  } else {
    Serial.print("Error sending Sensor Data: "); Serial.println(httpResponseCode);
    Serial.printf("[HTTP] POST Error: %s\n", http.errorToString(httpResponseCode).c_str());
  }
  http.end();
}

void sendStatusUpdate() {
  if (WiFi.status() != WL_CONNECTED) { Serial.println("sendStatusUpdate: WiFi Disconnected."); return; }

  HTTPClient http; http.begin(statusUpdateUrl); http.addHeader("Content-Type", "application/json");
   // *** FIX: Specify the size for StaticJsonDocument ***
  StaticJsonDocument<256> doc; // Allocate 256 bytes (adjust if needed)

  doc["tower_id"] = TOWER_ID_INT;
  doc["status"] = currentTowerStatus; // Use the dynamically determined status
  if (currentTowerStatus == "faulty" && currentFaultType != "") { doc["fault_type"] = currentFaultType; }

  String jsonPayload; serializeJson(doc, jsonPayload);

  Serial.print(millis()); Serial.print(" Sending Status Update: "); Serial.println(jsonPayload);
  int httpResponseCode = http.POST(jsonPayload);

  if (httpResponseCode > 0) {
    Serial.print("Status Update Response Code: "); Serial.println(httpResponseCode);
    if (httpResponseCode != HTTP_CODE_OK) { String responsePayload = http.getString(); Serial.println("Response: " + responsePayload); }
  } else {
    Serial.print("Error sending Status Update: "); Serial.println(httpResponseCode);
    Serial.printf("[HTTP] POST Error: %s\n", http.errorToString(httpResponseCode).c_str());
  }
  http.end();
}
