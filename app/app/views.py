from django.contrib import messages
from django.contrib.auth import login, logout
from django.contrib.auth.decorators import login_required
from django.shortcuts import redirect
from django.utils.decorators import method_decorator
from django.views import View


@method_decorator(login_required(login_url='login'), name='dispatch')
class LogoutView(View):
    def get(self, request):
        """
        Logs out the user.
        """
        logout(request)
        if request.user.is_authenticated:
            return redirect(login)
        return redirect('monitoring:landing')
